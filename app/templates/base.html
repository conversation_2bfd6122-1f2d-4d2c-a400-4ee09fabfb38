<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Biometric Attendance System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-fingerprint"></i> MIKAS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    
                    {% if current_user.role == 'admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i> Administration
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_students') }}">
                                    <i class="bi bi-people"></i> Students
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_lecturers') }}">
                                    <i class="bi bi-person-badge"></i> Lecturers
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_courses') }}">
                                    <i class="bi bi-book"></i> Courses
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_lectures') }}">
                                    <i class="bi bi-calendar-event"></i> Lectures
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('admin_system_settings') }}">
                                    <i class="bi bi-sliders"></i> System Settings
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.role == 'lecturer' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('lecturer_courses') }}">
                            <i class="bi bi-book"></i> My Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('start_lecture') }}">
                            <i class="bi bi-play-circle"></i> Start Lecture
                        </a>
                    </li>
                    {% endif %}
                    
                    {% if current_user.role == 'student' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('student_attendance') }}">
                            <i class="bi bi-calendar-check"></i> My Attendance
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_all_notifications') }}">
                            <i class="bi bi-bell"></i> All Notifications
                            {% if current_user.is_authenticated %}
                            <span id="notification-badge" class="badge bg-danger rounded-pill" style="{% if not notifications %}display: none;{% endif %}">
                                {{ notifications|length }}
                            </span>
                            {% endif %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                <i class="bi bi-person"></i> Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="bi bi-box-arrow-in-right"></i> Login
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main class="container py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2025 Dosumu Attendance System</span>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
    {% block scripts %}
    <script>
        // Function to update notification count
        function updateNotificationCount() {
            fetch('/api/notifications/count')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notification-badge');
                    if (badge) {
                        if (data.count > 0) {
                            badge.textContent = data.count;
                            badge.style.display = 'inline-block';
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        }
        
        // Update notification count every 60 seconds
        document.addEventListener('DOMContentLoaded', function() {
            // Initial update
            updateNotificationCount();
            
            // Set interval for updates
            setInterval(updateNotificationCount, 60000);
        });
    </script>
    {% endblock %}
</body>
</html>