{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.25/jspdf.plugin.autotable.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attendance status chart
        const statusCtx = document.getElementById('attendanceStatusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['Present', 'Late', 'Absent'],
                datasets: [{
                    data: [{{ present_count }}, {{ late_count }}, {{ absent_count }}],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'Attendance Status'
                    }
                }
            }
        });

        // Time distribution chart
        const timeCtx = document.getElementById('entryTimeChart').getContext('2d');
        const timeData = {{ entry_time_data|tojson }};
        const timeChart = new Chart(timeCtx, {
            type: 'bar',
            data: {
                labels: timeData.labels,
                datasets: [{
                    label: 'Number of Students',
                    data: timeData.values,
                    backgroundColor: 'rgba(54, 162, 235, 0.7)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Entry Time Distribution'
                    }
                }
            }
        });

        // Search and filtering
        const searchInput = document.getElementById('searchInput');
        const statusFilter = document.getElementById('statusFilter');
        const departmentFilter = document.getElementById('departmentFilter');
        const resetButton = document.getElementById('resetFilters');
        const rows = document.querySelectorAll('.student-row');
        
        function applyFilters() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;
            const departmentValue = departmentFilter.value;
            
            rows.forEach(row => {
                const name = row.getAttribute('data-name');
                const matric = row.getAttribute('data-matric');
                const department = row.getAttribute('data-department');
                const status = row.getAttribute('data-status');
                
                const matchesSearch = name.includes(searchTerm) || 
                                     matric.includes(searchTerm);
                                     
                const matchesStatus = statusValue === 'all' || status === statusValue;
                
                const matchesDepartment = departmentValue === 'all' || 
                                         department === departmentValue.toLowerCase();
                
                if (matchesSearch && matchesStatus && matchesDepartment) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        searchInput.addEventListener('input', applyFilters);
        statusFilter.addEventListener('change', applyFilters);
        departmentFilter.addEventListener('change', applyFilters);
        
        resetButton.addEventListener('click', function() {
            searchInput.value = '';
            statusFilter.value = 'all';
            departmentFilter.value = 'all';
            rows.forEach(row => row.style.display = '');
        });
        
        // Export to CSV
        document.getElementById('exportCSV').addEventListener('click', function() {
            const table = document.getElementById('attendanceTable');
            let csv = [];
            
            // Get headers
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => headers.push(cell.textContent.trim()));
            csv.push(headers.join(','));
            
            // Get visible rows
            const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                                    .filter(row => row.style.display !== 'none');
            
            visibleRows.forEach(row => {
                const rowData = [];
                row.querySelectorAll('td').forEach(cell => {
                    // Replace commas with spaces to avoid CSV issues
                    let text = cell.textContent.trim().replace(/,/g, ' ');
                    // If cell contains a badge, get just the text
                    if (cell.querySelector('.badge')) {
                        text = cell.querySelector('.badge').textContent.trim();
                    }
                    rowData.push(text);
                });
                csv.push(rowData.join(','));
            });
            
            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', 'attendance_report.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
        
        // Export to PDF
        document.getElementById('exportPDF').addEventListener('click', function() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Add title
            doc.setFontSize(18);
            doc.text('Attendance Report', 14, 22);
            
            // Add lecture info
            doc.setFontSize(12);
            doc.text(`Course: {{ lecture.course }}`, 14, 30);
            doc.text(`Date: {{ lecture.date }}`, 14, 36);
            doc.text(`Time: {{ lecture.start_time }} - {{ lecture.end_time }}`, 14, 42);
            doc.text(`Lecturer: {{ lecture.lecturer }}`, 14, 48);
            
            // Add attendance table
            const table = document.getElementById('attendanceTable');
            const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                                    .filter(row => row.style.display !== 'none');
            
            const tableData = [];
            const tableHeaders = [];
            
            // Get headers
            table.querySelectorAll('thead th').forEach(cell => {
                tableHeaders.push(cell.textContent.trim());
            });
            
            // Get data from visible rows
            visibleRows.forEach(row => {
                const rowData = [];
                row.querySelectorAll('td').forEach(cell => {
                    // If cell contains a badge, get just the text
                    if (cell.querySelector('.badge')) {
                        rowData.push(cell.querySelector('.badge').textContent.trim());
                    } else {
                        rowData.push(cell.textContent.trim());
                    }
                });
                tableData.push(rowData);
            });
            
            // Generate table in PDF
            doc.autoTable({
                head: [tableHeaders],
                body: tableData,
                startY: 55,
                theme: 'grid',
                styles: { fontSize: 10 },
                headStyles: { fillColor: [41, 128, 185] }
            });
            
            // Add summary
            const finalY = doc.lastAutoTable.finalY + 10;
            doc.text(`Total Students: {{ attendance|length }}`, 14, finalY);
            doc.text(`Present: {{ present_count }}`, 14, finalY + 6);
            doc.text(`Late: {{ late_count }}`, 14, finalY + 12);
            doc.text(`Absent: {{ absent_count }}`, 14, finalY + 18);
            doc.text(`Attendance Rate: {{ attendance_percentage }}`, 14, finalY + 24);
            
            // Save PDF
            doc.save('attendance_report.pdf');
        });
    });
</script>
{% endblock %}

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Attendance Status</h5>
            </div>
            <div class="card-body">
                <canvas id="attendanceStatusChart" width="100%" height="250"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Entry Time Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="entryTimeChart" width="100%" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Attendance Records</h5>
        <div>
            <button class="btn btn-sm btn-light" id="exportCSV">
                <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
            </button>
            <button class="btn btn-sm btn-light" id="exportPDF">
                <i class="bi bi-file-earmark-pdf"></i> Export PDF
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" id="searchInput" class="form-control" placeholder="Search students...">
                </div>
            </div>
            <div class="col-md-3">
                <select id="statusFilter" class="form-select">
                    <option value="all">All Status</option>
                    <option value="present">Present</option>
                    <option value="late">Late</option>
                    <option value="absent">Absent</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="departmentFilter" class="form-select">
                    <option value="all">All Departments</option>
                    {% for dept in departments %}
                    <option value="{{ dept }}">{{ dept }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button id="resetFilters" class="btn btn-outline-secondary w-100">
                    <i class="bi bi-x-circle"></i> Reset
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="attendanceTable">
                <thead>
                    <tr>
                        <th>Student Name</th>
                        <th>Matric Number</th>
                        <th>Department</th>
                        <th>Status</th>
                        <th>Entry Time</th>
                        <th>Exit Time</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance %}
                    <tr class="student-row" 
                        data-name="{{ record.name|lower }}" 
                        data-matric="{{ record.matric_number|lower }}" 
                        data-department="{{ record.department|lower }}" 
                        data-status="{{ record.status|lower }}">
                        <td>{{ record.name }}</td>
                        <td>{{ record.matric_number }}</td>
                        <td>{{ record.department }}</td>
                        <td>
                            {% if record.status == 'present' %}
                            <span class="badge bg-success">Present</span>
                            {% elif record.status == 'late' %}
                            <span class="badge bg-warning">Late</span>
                            {% else %}
                            <span class="badge bg-danger">Absent</span>
                            {% endif %}
                        </td>
                        <td>{{ record.entry_time|default('--') }}</td>
                        <td>{{ record.exit_time|default('--') }}</td>
                        <td>{{ record.duration|default('--') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
