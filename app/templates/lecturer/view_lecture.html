{% extends "base.html" %}

{% block title %}View Lecture - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Lecture Details</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if lecture.is_active %}
        <form method="POST" action="{{ url_for('end_lecture', lecture_id=lecture_id) }}" class="me-2" onsubmit="return confirm('Are you sure you want to end this lecture? This will process absences and send notifications.');">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-sm btn-danger">
                <i class="bi bi-stop-circle"></i> End Lecture
            </button>
        </form>
        {% endif %}
        <a href="{{ url_for('export_lecture', lecture_id=lecture_id) }}" class="btn btn-sm btn-success me-2">
            <i class="bi bi-download"></i> Export Attendance
        </a>
        <a href="{{ url_for('dashboard') }}" class="btn btn-sm btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

{% if lecture.is_active and lecture.duration_minutes - elapsed_minutes <= 10 %}
<div class="alert alert-warning">
    <strong>Warning!</strong> This lecture will automatically end in 
    <span id="countdown-timer">{{ lecture.duration_minutes - elapsed_minutes }}:00</span> minutes.
</div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Lecture Info</h5>
            </div>
            <div class="card-body">
                <p><strong>Course:</strong> {{ lecture.course_code }}: {{ lecture.course_name }}</p>
                <p><strong>Date:</strong> {{ lecture.start_time|datetime }}</p>
                <p><strong>Duration:</strong> {{ lecture.duration_minutes }} minutes</p>
                {% if lecture.is_active %}
                <p><strong>Elapsed Time:</strong> {{ elapsed_minutes }} minutes</p>
                <p><strong>Remaining Time:</strong> {{ lecture.duration_minutes - elapsed_minutes if elapsed_minutes < lecture.duration_minutes else 0 }} minutes</p>
                {% endif %}
                <p><strong>Status:</strong> 
                    {% if lecture.is_active %}
                    <span class="badge bg-success">Active</span>
                    {% else %}
                    <span class="badge bg-secondary">Completed</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Attendance Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <h2>{{ attendance|length }}</h2>
                        <p>Total Students</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h2>{{ attendance_percentage|default('0%') }}</h2>
                        <p>Attendance Rate</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h2>{{ late_count|default(0) }}</h2>
                        <p>Late Arrivals</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Attendance List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Student Name</th>
                        <th>Matric Number</th>
                        <th>Time In</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance %}
                    <tr>
                        <td>{{ record[0] }}</td>
                        <td>{{ record[1] }}</td>
                        <td>{{ record[2] }}</td>
                        <td>
                            {% if record[3] %}
                            <span class="badge bg-warning">Late</span>
                            {% else %}
                            <span class="badge bg-success">On Time</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    {% if not attendance %}
                    <tr>
                        <td colspan="4" class="text-center">No attendance records found</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Auto-refresh the page every minute if the lecture is active
    {% if lecture.is_active %}
    setTimeout(function() {
        window.location.reload();
    }, 60000); // Refresh every 60 seconds
    
    // Add countdown timer if lecture is about to end
    {% if lecture.duration_minutes - elapsed_minutes <= 10 %}
    // Calculate remaining seconds
    let remainingSeconds = {{ (lecture.duration_minutes - elapsed_minutes) * 60 }};
    
    // Update countdown every second
    const countdownInterval = setInterval(function() {
        remainingSeconds--;
        
        if (remainingSeconds <= 0) {
            clearInterval(countdownInterval);
            // Reload the page to show the lecture has ended
            window.location.reload();
            return;
        }
        
        // Calculate minutes and seconds
        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        
        // Update countdown display
        const countdownElement = document.getElementById('countdown-timer');
        if (countdownElement) {
            countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        }
    }, 1000);
    {% endif %}
    {% endif %}
    
    // Function to start the camera for attendance
    function startAttendanceCamera() {
        if ({{ lecture.is_active|tojson }}) {
            console.log('Attempting to start camera server for lecture ID: {{ lecture_id }}');
            
            // Show a loading message
            const loadingAlert = document.createElement('div');
            loadingAlert.className = 'alert alert-info alert-dismissible fade show';
            loadingAlert.id = 'camera-loading-alert';
            loadingAlert.innerHTML = `
                <strong>Starting camera...</strong> Please wait while the system initializes.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            document.querySelector('.container').prepend(loadingAlert);
            
            // Make an AJAX call to start the camera server
            fetch('/api/start_camera_server', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    lecture_id: {{ lecture_id }}
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                // Remove the loading alert
                const loadingAlert = document.getElementById('camera-loading-alert');
                if (loadingAlert) loadingAlert.remove();
                
                if (data.success) {
                    console.log('Camera server started successfully');
                    // Show a success message to the user
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <strong>Success!</strong> Camera system activated for attendance tracking.
                        <p>If the camera is not on, please check that:</p>
                        <ol>
                            <li>The Kinect device is properly connected</li>
                            <li>The Kinect client application is running</li>
                            <li>The socket server is running on port 9090</li>
                        </ol>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').prepend(alertDiv);
                } else {
                    console.error('Failed to start camera server:', data.error);
                    // Show an error message to the user
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <strong>Error!</strong> Failed to start camera system: ${data.error}
                        <p>Please make sure the socket server is running and the Kinect device is connected.</p>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').prepend(alertDiv);
                }
            })
            .catch(error => {
                // Remove the loading alert
                const loadingAlert = document.getElementById('camera-loading-alert');
                if (loadingAlert) loadingAlert.remove();
                
                console.error('Error starting camera server:', error);
                // Show an error message to the user
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <strong>Error!</strong> Failed to communicate with the camera system: ${error.message}
                    <p>Please make sure the socket server is running and the Kinect device is connected.</p>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                document.querySelector('.container').prepend(alertDiv);
            });
        } else {
            console.log('Lecture is not active, not starting camera');
        }
    }

    // Start the camera when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, starting attendance camera');
        startAttendanceCamera();
        
        // Auto-refresh the page every 60 seconds if the lecture is active
        {% if lecture.is_active %}
        setInterval(function() {
            // Reload the page to update lecture information
            location.reload();
        }, 60000); // 60 seconds
        {% endif %}
    });
</script>
{% endblock %}