{% extends "base.html" %}

{% block title %}My Courses - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Assigned Courses</h1>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Courses You Teach</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Course Code</th>
                        <th>Title</th>
                        <th>Department</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for course in courses %}
                    <tr>
                        <td>{{ course.code }}</td>
                        <td>{{ course.title }}</td>
                        <td>{{ course.department }}</td>
                        <td>
                            <a href="{{ url_for('start_lecture') }}?course={{ course.id }}" class="btn btn-sm btn-success">
                                <i class="bi bi-play-circle"></i> Start Lecture
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                    {% if not courses %}
                    <tr>
                        <td colspan="4" class="text-center">You have no assigned courses</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="alert alert-info mt-4">
    <h5><i class="bi bi-info-circle"></i> Voice Recognition</h5>
    <p>You can also start a lecture using voice commands. When using the voice recognition system, you'll be asked to specify which course you're teaching.</p>
    <p>Available course codes: {% for course in courses %}{{ course.code }}{% if not loop.last %}, {% endif %}{% endfor %}</p>
</div>
{% endblock %}
