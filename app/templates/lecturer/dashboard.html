{% extends "base.html" %}

{% block title %}Lecturer Dashboard - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Lecturer Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('start_lecture') }}" class="btn btn-sm btn-primary">
            <i class="bi bi-play-circle"></i> Start New Lecture
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Total Lectures</h5>
                        <h2 class="card-text">{{ lectures|length }}</h2>
                    </div>
                    <i class="bi bi-calendar-event fs-1"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Active Lectures</h5>
                        <h2 class="card-text">{{ active_lectures|default(0) }}</h2>
                    </div>
                    <i class="bi bi-play-circle fs-1"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<h4 class="mb-3">Recent Lectures</h4>
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Course</th>
                <th>Date & Time</th>
                <th>Duration</th>
                <th>Attendance</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for lecture in lectures %}
            <tr>
                <td>{{ lecture.course }}</td>
                <td>{{ lecture.start_time }}</td>
                <td>{{ lecture.duration_minutes }} mins</td>
                <td>{{ lecture.attendance_count|default(0) }} students</td>
                <td>
                    <a href="{{ url_for('view_lecture', lecture_id=lecture.id) }}" class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i> View
                    </a>
                    <a href="{{ url_for('export_lecture', lecture_id=lecture.id) }}" class="btn btn-sm btn-success">
                        <i class="bi bi-download"></i> Export
                    </a>
                </td>
            </tr>
            {% endfor %}
            {% if not lectures %}
            <tr>
                <td colspan="5" class="text-center">No lectures found</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Attendance Statistics</h5>
            </div>
            <div class="card-body">
                <canvas id="attendanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Course Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="courseChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attendance statistics chart
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        const attendanceChart = new Chart(attendanceCtx, {
            type: 'pie',
            data: {
                labels: ['On Time', 'Late', 'Absent'],
                datasets: [{
                    label: 'Attendance',
                    data: [
                        {{ attendance_stats.on_time }}, 
                        {{ attendance_stats.late }}, 
                        {{ attendance_stats.absent }}
                    ],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(255, 99, 132, 0.5)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            }
        });
        
        // Course distribution chart
        const courseCtx = document.getElementById('courseChart').getContext('2d');
        const courseChart = new Chart(courseCtx, {
            type: 'pie',
            data: {
                labels: {{ course_labels|tojson }},
                datasets: [{
                    label: 'Lectures per Course',
                    data: {{ course_values|tojson }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.5)',
                        'rgba(54, 162, 235, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(153, 102, 255, 0.5)',
                        'rgba(255, 159, 64, 0.5)',
                        'rgba(199, 199, 199, 0.5)',
                        'rgba(83, 102, 255, 0.5)',
                        'rgba(40, 159, 64, 0.5)',
                        'rgba(210, 199, 199, 0.5)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)',
                        'rgba(83, 102, 255, 1)',
                        'rgba(40, 159, 64, 1)',
                        'rgba(210, 199, 199, 1)'
                    ],
                    borderWidth: 1
                }]
            }
        });
    });
</script>
{% endblock %}