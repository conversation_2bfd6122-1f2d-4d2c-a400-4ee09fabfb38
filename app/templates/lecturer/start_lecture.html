{% extends "base.html" %}

{% block title %}Start New Lecture - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Start New Lecture</h1>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Lecture Details</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="course" class="form-label">Course</label>
                        <select class="form-control" id="course" name="course" required>
                            <option value="">-- Select Course --</option>
                            {% for course in lecturer_courses %}
                            <option value="{{ course.id }}" {% if selected_course == course.id|string %}selected{% endif %}>
                                {{ course.code }}: {{ course.title }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="duration" name="duration" required min="15" max="240" value="60">
                        <div class="form-text">Minimum 15 minutes, maximum 4 hours</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="room" class="form-label">Room (optional)</label>
                        <input type="text" class="form-control" id="room" name="room" placeholder="e.g., Room 101">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-play-circle"></i> Start Lecture
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Instructions</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>Select the course for this lecture session.</li>
                    <li>Set the expected duration of the lecture.</li>
                    <li>Optionally specify the room location.</li>
                    <li>Click "Start Lecture" to begin the attendance tracking.</li>
                    <li>Students will be able to register their attendance using facial recognition.</li>
                </ol>
                
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Make sure the biometric capture device is properly connected and configured.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}