{% extends 'base.html' %}

{% block title %}My Profile{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">My Profile</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('profile') }}">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-control" value="{{ lecturer.name }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" value="{{ lecturer.username }}" readonly>
                            </div>
                        </div>
                        
                        <hr>
                        <h5>Contact Information</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="{{ lecturer.email or '' }}">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-control" name="phone" value="{{ lecturer.phone or '' }}">
                            </div>
                        </div>
                        
                        <hr>
                        <h5>Notification Preferences</h5>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications"
                                       {% if lecturer.email_notifications == 'true' %}checked{% endif %}>
                                <label class="form-check-label" for="emailNotifications">
                                    Receive email notifications
                                </label>
                            </div>
                            <div class="form-text text-muted">
                                You'll receive notifications about lecture attendance, system events, and announcements.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="sms_notifications" id="smsNotifications"
                                       {% if lecturer.sms_notifications == 'true' %}checked{% endif %}>
                                <label class="form-check-label" for="smsNotifications">
                                    Receive SMS notifications
                                </label>
                            </div>
                            <div class="form-text text-muted">
                                You'll receive SMS alerts for important notifications like low attendance.
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
```
</aug