<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Attendance Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .student-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .student-info p {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status-ontime {
            color: #28a745;
            font-weight: bold;
        }
        .status-late {
            color: #ffc107;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
        }
        .summary {
            margin: 20px 0;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Student Attendance Report</h1>
        <p>Generated on: {{ generated_date }}</p>
    </div>
    
    <div class="student-info">
        <h2>Student Information</h2>
        <p><strong>Name:</strong> {{ student.name }}</p>
        <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
        {% if student.email %}<p><strong>Email:</strong> {{ student.email }}</p>{% endif %}
    </div>
    
    <div class="filter-info">
        <h2>Report Filters</h2>
        <p><strong>Course:</strong> {{ course_filter or 'All Courses' }}</p>
        <p><strong>Date Range:</strong> {{ date_from or 'Beginning' }} to {{ date_to or 'Present' }}</p>
    </div>
    
    <div class="summary">
        <h2>Attendance Summary</h2>
        {% set total_lectures = attendance_data|length %}
        {% set late_count = attendance_data|selectattr('is_late', 'eq', 1)|list|length %}
        {% set ontime_count = total_lectures - late_count %}
        
        <p><strong>Total Lectures:</strong> {{ total_lectures }}</p>
        <p><strong>On Time:</strong> {{ ontime_count }} ({{ (ontime_count / total_lectures * 100)|round(1) if total_lectures > 0 else 0 }}%)</p>
        <p><strong>Late:</strong> {{ late_count }} ({{ (late_count / total_lectures * 100)|round(1) if total_lectures > 0 else 0 }}%)</p>
    </div>
    
    <h2>Attendance Records</h2>
    {% if attendance_data %}
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Course</th>
                    <th>Time</th>
                    <th>Duration</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for record in attendance_data %}
                    <tr>
                        <td>{{ record.lecture_date.split(' ')[0] }}</td>
                        <td>
                            <strong>{{ record.course_code }}</strong><br>
                            {{ record.course_name }}
                        </td>
                        <td>{{ record.attendance_time.split(' ')[1] }}</td>
                        <td>{{ record.duration_minutes }} mins</td>
                        <td class="{% if record.is_late %}status-late{% else %}status-ontime{% endif %}">
                            {{ 'Late' if record.is_late else 'On Time' }}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>No attendance records found matching the criteria.</p>
    {% endif %}
    
    <div class="footer">
        <p>This is an official attendance report from the Biometric Attendance System.</p>
        <p>© {{ generated_date.split('-')[0] }} University Biometric Attendance System</p>
    </div>
</body>
</html>