{% extends 'base.html' %}

{% block title %}Student Dashboard{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Welcome, {{ student.name }}</h1>
    
    <div class="row">
        <!-- Student Profile Card -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Student Profile</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> {{ student.name }}</p>
                    <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                    <p><strong>Email:</strong> {{ student.email or 'Not set' }}</p>
                    <p><strong>Phone:</strong> {{ student.phone or 'Not set' }}</p>
                </div>
            </div>
        </div>
        
        <!-- Attendance Statistics -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Attendance Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <h2>{{ "%.1f"|format(attendance_rate) }}%</h2>
                            <p>Attendance Rate</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h2>{{ total_lectures }}</h2>
                            <p>Total Lectures</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <h2>{{ late_count }}</h2>
                            <p>Late Arrivals</p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <canvas id="attendanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Upcoming Lectures -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Upcoming Lectures</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_lectures %}
                        <div class="list-group">
                            {% for lecture in upcoming_lectures %}
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">{{ lecture.course_code }}: {{ lecture.course_name }}</h5>
                                        <small>{{ lecture.duration_minutes }} mins</small>
                                    </div>
                                    <p class="mb-1">
                                        <i class="bi bi-calendar"></i> 
                                        {{ lecture.start_time|datetime }}
                                    </p>
                                    <small>Lecturer: {{ lecture.lecturer_name }}</small>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">No upcoming lectures scheduled.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Recent Attendance -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning">
                    <h5 class="card-title mb-0">Recent Attendance</h5>
                </div>
                <div class="card-body">
                    {% if attendance_history %}
                        <div class="list-group">
                            {% for record in attendance_history[:5] %}
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">{{ record.course_code }}</h5>
                                        <small>{{ record.attendance_time|datetime }}</small>
                                    </div>
                                    <p class="mb-1">{{ record.course_name }}</p>
                                    {% if record.is_late %}
                                        <span class="badge bg-warning">Late</span>
                                    {% else %}
                                        <span class="badge bg-success">On Time</span>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                        <div class="mt-3 text-end">
                            <a href="{{ url_for('student_attendance') }}" class="btn btn-outline-primary btn-sm">
                                View All Attendance Records
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted">No attendance records found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attendance chart
        var ctx = document.getElementById('attendanceChart').getContext('2d');
        var attendanceChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Present', 'Absent', 'Late'],
                datasets: [{
                    data: [
                        {{ attendance_history|length - late_count }}, 
                        {{ total_lectures - attendance_history|length }}, 
                        {{ late_count }}
                    ],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 206, 86, 0.7)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(255, 206, 86, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    });
</script>
{% endblock %}