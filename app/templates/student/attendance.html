{% extends 'base.html' %}

{% block title %}My Attendance{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">My Attendance Records</h1>
    
    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="course" class="form-label">Course</label>
                    <select class="form-select" id="course" name="course">
                        <option value="">All Courses</option>
                        {% for course in courses %}
                            <option value="{{ course.code }}" {% if course_filter == course.code %}selected{% endif %}>
                                {{ course.code }}: {{ course.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Attendance Records Table -->
    <div class="card">
        <div class="card-body">
            {% if attendance_history %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Course</th>
                                <th>Time</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in attendance_history %}
                                <tr>
                                    <td>{{ record.lecture_date|date }}</td>
                                    <td>
                                        <strong>{{ record.course_code }}</strong><br>
                                        <small>{{ record.course_name }}</small>
                                    </td>
                                    <td>{{ record.attendance_time|time }}</td>
                                    <td>{{ record.duration_minutes }} mins</td>
                                    <td>
                                        {% if record.is_late %}
                                            <span class="badge bg-warning">Late</span>
                                        {% else %}
                                            <span class="badge bg-success">On Time</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Export Options -->
                <div class="mt-3">
                    <a href="{{ url_for('export_student_attendance', format='csv', student_id=student.id, course=course_filter, date_from=date_from, date_to=date_to) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-file-earmark-excel"></i> Export to CSV
                    </a>
                    <a href="{{ url_for('export_student_attendance', format='pdf', student_id=student.id, course=course_filter, date_from=date_from, date_to=date_to) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-file-earmark-pdf"></i> Export to PDF
                    </a>
                </div>
            {% else %}
                <div class="alert alert-info">
                    No attendance records found matching your criteria.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}


