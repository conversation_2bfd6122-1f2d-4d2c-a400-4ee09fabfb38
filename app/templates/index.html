{% extends "base.html" %}

{% block title %}Welcome - MIKAS{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-8 text-center">
            <h1 class="display-4 mb-4">MIKAS-MultiKinect Attendance System</h1>
            <p class="lead mb-5">A secure and efficient way to track attendance using facial recognition technology.</p>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <i class="bi bi-person-check fs-1 text-primary mb-3"></i>
                            <h5 class="card-title">Automated Attendance</h5>
                            <p class="card-text">No more manual roll calls or sign-in sheets. Attendance is captured automatically.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <i class="bi bi-shield-check fs-1 text-success mb-3"></i>
                            <h5 class="card-title">Secure Authentication</h5>
                            <p class="card-text">Biometric verification ensures that only registered students can mark attendance.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <i class="bi bi-graph-up fs-1 text-info mb-3"></i>
                            <h5 class="card-title">Detailed Analytics</h5>
                            <p class="card-text">Generate reports and analyze attendance patterns with comprehensive dashboards.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg px-4 me-2">
                    <i class="bi bi-box-arrow-in-right"></i> Login
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}