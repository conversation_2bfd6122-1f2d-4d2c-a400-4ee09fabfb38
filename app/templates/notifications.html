{% extends 'base.html' %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Notifications</h1>
        <button id="markAllRead" class="btn btn-outline-secondary">
            <i class="bi bi-check-all"></i> Mark All as Read
        </button>
    </div>
    
    <div class="card">
        <div class="card-body">
            {% if notifications %}
                <div class="list-group">
                    {% for notification in notifications %}
                        <div class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-light{% endif %}" 
                             id="notification-{{ notification.id }}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">
                                    {% if notification.type == 'absence' %}
                                        <i class="bi bi-calendar-x text-danger"></i>
                                    {% elif notification.type == 'late' %}
                                        <i class="bi bi-clock-history text-warning"></i>
                                    {% elif notification.type == 'system' %}
                                        <i class="bi bi-gear text-info"></i>
                                    {% else %}
                                        <i class="bi bi-bell"></i>
                                    {% endif %}
                                    {{ notification.type|title }} Notification
                                </h5>
                                <small>{{ notification.timestamp|datetime }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            <div class="d-flex justify-content-end mt-2">
                                {% if not notification.is_read %}
                                    <button class="btn btn-sm btn-outline-primary me-2 mark-read-btn" 
                                            data-id="{{ notification.id }}">
                                        <i class="bi bi-check"></i> Mark as Read
                                    </button>
                                {% endif %}
                                <button class="btn btn-sm btn-outline-danger delete-btn" 
                                        data-id="{{ notification.id }}">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> You have no notifications.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mark single notification as read
        document.querySelectorAll('.mark-read-btn').forEach(button => {
            button.addEventListener('click', function() {
                const notificationId = this.getAttribute('data-id');
                
                fetch(`/notifications/mark_read/${notificationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI
                        const notification = document.getElementById(`notification-${notificationId}`);
                        notification.classList.remove('list-group-item-light');
                        this.remove();
                        
                        // Update notification count in navbar
                        updateNotificationCount();
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        });
        
        // Mark all notifications as read
        document.getElementById('markAllRead').addEventListener('click', function() {
            fetch('/notifications/mark_all_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI
                    document.querySelectorAll('.list-group-item-light').forEach(item => {
                        item.classList.remove('list-group-item-light');
                    });
                    
                    document.querySelectorAll('.mark-read-btn').forEach(btn => {
                        btn.remove();
                    });
                    
                    // Update notification count in navbar
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
        });
        
        // Delete notification
        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', function() {
                const notificationId = this.getAttribute('data-id');
                
                fetch(`/notifications/delete/${notificationId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove notification from UI
                        const notification = document.getElementById(`notification-${notificationId}`);
                        notification.remove();
                        
                        // If no notifications left, show the "no notifications" message
                        if (document.querySelectorAll('.list-group-item').length === 0) {
                            const listGroup = document.querySelector('.list-group');
                            listGroup.innerHTML = `
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> You have no notifications.
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        });
        
        // Function to update notification count in navbar
        function updateNotificationCount() {
            fetch('/api/notifications/count')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notification-badge');
                    if (badge) {
                        if (data.count > 0) {
                            badge.textContent = data.count;
                            badge.style.display = 'inline-block';
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    });
</script>
{% endblock %}