{% extends "base.html" %}

{% block title %}Manage Students - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between mb-4">
    <h2>Student Management</h2>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
            <i class="bi bi-person-plus"></i> Add Student
        </button>
        <button id="importFacesBtn" class="btn btn-secondary">
            <i class="bi bi-person-bounding-box"></i> Import Face Encodings
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" id="studentSearch" class="form-control" placeholder="Search by name or matric number...">
        </div>
    </div>
    <div class="col-md-3">
        <select id="departmentFilter" class="form-select">
            <option value="all">All Departments</option>
            {% set departments = [] %}
            {% for student in students %}
                {% if student.department and student.department not in departments %}
                    {% set _ = departments.append(student.department) %}
                    <option value="{{ student.department }}">{{ student.department }}</option>
                {% endif %}
            {% endfor %}
        </select>
    </div>
    <div class="col-md-3">
        <select id="faceFilter" class="form-select">
            <option value="all">All Face Status</option>
            <option value="yes">Face Registered</option>
            <option value="no">Face Not Registered</option>
        </select>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Matric Number</th>
                <th>Department</th>
                <th>Face Registered</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for student in students %}
            <tr>
                <td>{{ student.id }}</td>
                <td>{{ student.name }}</td>
                <td>{{ student.matric_number }}</td>
                <td>{{ student.department }}</td>
                <td>
                    {% if student.has_face %}
                    <span class="badge bg-success">Yes</span>
                    {% else %}
                    <span class="badge bg-danger">No</span>
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewStudentModal{{ student.id }}">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editStudentModal{{ student.id }}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteStudentModal{{ student.id }}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Add Student Modal -->
<div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStudentModalLabel">Add New Student</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addStudentForm" method="post" action="{{ url_for('admin_add_student') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="matric_number" class="form-label">Matric Number</label>
                        <input type="text" class="form-control" id="matric_number" name="matric_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department" name="department" required>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addStudentForm" class="btn btn-primary">Add Student</button>
            </div>
        </div>
    </div>
</div>

<!-- View/Edit/Delete Modals for Students -->
{% for student in students %}
<!-- View Student Modal -->
<div class="modal fade" id="viewStudentModal{{ student.id }}" tabindex="-1" aria-labelledby="viewStudentModalLabel{{ student.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewStudentModalLabel{{ student.id }}">Student Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Name:</strong> {{ student.name }}
                </div>
                <div class="mb-3">
                    <strong>Matric Number:</strong> {{ student.matric_number }}
                </div>
                <div class="mb-3">
                    <strong>Department:</strong> {{ student.department }}
                </div>
                <div class="mb-3">
                    <strong>Username:</strong> {{ student.username or 'Not assigned' }}
                </div>
                <div class="mb-3">
                    <strong>Face Registered:</strong> 
                    {% if student.has_face %}
                    <span class="badge bg-success">Yes</span>
                    {% else %}
                    <span class="badge bg-danger">No</span>
                    {% endif %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Student Modal -->
<div class="modal fade" id="editStudentModal{{ student.id }}" tabindex="-1" aria-labelledby="editStudentModalLabel{{ student.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStudentModalLabel{{ student.id }}">Edit Student</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editStudentForm{{ student.id }}" method="post" action="{{ url_for('admin_edit_student', student_id=student.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name{{ student.id }}" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name{{ student.id }}" name="name" value="{{ student.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="matric_number{{ student.id }}" class="form-label">Matric Number</label>
                        <input type="text" class="form-control" id="matric_number{{ student.id }}" name="matric_number" value="{{ student.matric_number }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="department{{ student.id }}" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department{{ student.id }}" name="department" value="{{ student.department }}" required>
                    </div>
                    {% if not student.username %}
                    <div class="mb-3">
                        <label for="username{{ student.id }}" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username{{ student.id }}" name="username" placeholder="Assign username">
                    </div>
                    <div class="mb-3">
                        <label for="password{{ student.id }}" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password{{ student.id }}" name="password" placeholder="Assign password">
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editStudentForm{{ student.id }}" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Student Modal -->
<div class="modal fade" id="deleteStudentModal{{ student.id }}" tabindex="-1" aria-labelledby="deleteStudentModalLabel{{ student.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteStudentModalLabel{{ student.id }}">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete student <strong>{{ student.name }}</strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{{ url_for('admin_delete_student', student_id=student.id) }}" class="btn btn-danger">Delete Student</a>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<script>
// Search and filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('studentSearch');
    const departmentFilter = document.getElementById('departmentFilter');
    const faceFilter = document.getElementById('faceFilter');
    const tableRows = document.querySelectorAll('tbody tr');
    
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase();
        const departmentValue = departmentFilter.value;
        const faceValue = faceFilter.value;
        
        tableRows.forEach(row => {
            const name = row.cells[1].textContent.toLowerCase();
            const matricNumber = row.cells[2].textContent.toLowerCase();
            const department = row.cells[3].textContent.toLowerCase();
            const hasFace = row.cells[4].querySelector('.badge').textContent === 'Yes';
            
            const matchesSearch = name.includes(searchTerm) || matricNumber.includes(searchTerm);
            const matchesDepartment = departmentValue === 'all' || department === departmentValue;
            const matchesFace = faceValue === 'all' || 
                               (faceValue === 'yes' && hasFace) || 
                               (faceValue === 'no' && !hasFace);
            
            if (matchesSearch && matchesDepartment && matchesFace) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    searchInput.addEventListener('input', applyFilters);
    departmentFilter.addEventListener('change', applyFilters);
    faceFilter.addEventListener('change', applyFilters);
});

// Import faces functionality
document.getElementById('importFacesBtn').addEventListener('click', function() {
    if (confirm('Import face encodings from the images directory? This may take a few moments.')) {
        // Show loading indicator
        this.innerHTML = '<i class="bi bi-hourglass-split"></i> Importing...';
        this.disabled = true;
        
        fetch('/admin/import_faces', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            // Reset button
            this.innerHTML = '<i class="bi bi-person-bounding-box"></i> Import Face Encodings';
            this.disabled = false;
            
            if (data.success) {
                alert(data.message || 'Face encodings imported successfully!');
                location.reload(); // Reload to show updated face encoding status
            } else {
                alert('Error importing face encodings: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            // Reset button
            this.innerHTML = '<i class="bi bi-person-bounding-box"></i> Import Face Encodings';
            this.disabled = false;
            
            console.error('Error:', error);
            alert('An error occurred while importing face encodings.');
        });
    }
});
</script>

{% endblock %}