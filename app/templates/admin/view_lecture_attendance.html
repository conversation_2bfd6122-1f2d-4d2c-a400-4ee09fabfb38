{% extends "base.html" %}

{% block title %}Lecture Attendance - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Lecture Attendance Details</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin_export_lecture', lecture_id=lecture.id) }}" class="btn btn-sm btn-success me-2">
            <i class="bi bi-download"></i> Export Attendance
        </a>
        <a href="{{ url_for('admin_lectures') }}" class="btn btn-sm btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Lectures
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Lecture Information</h5>
            </div>
            <div class="card-body">
                <p><strong>Course:</strong> {{ lecture.course }}</p>
                <p><strong>Lecturer:</strong> {{ lecture.lecturer_name }}</p>
                <p><strong>Date:</strong> {{ lecture.start_time }}</p>
                <p><strong>Duration:</strong> {{ lecture.duration_minutes }} minutes</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Attendance Summary</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h2>{{ attendance|length }}</h2>
                        <p>Total Students</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h2>{{ attendance_percentage|default('0%') }}</h2>
                        <p>Attendance Rate</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h2>{{ on_time_count|default(0) }}</h2>
                        <p>On Time</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h2>{{ late_count|default(0) }}</h2>
                        <p>Late</p>
                    </div>
                </div>
                <div class="mt-3">
                    <canvas id="attendanceChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Attendance List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Student Name</th>
                        <th>Matric Number</th>
                        <th>Department</th>
                        <th>Time In</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance %}
                    <tr>
                        <td>{{ record.student_name }}</td>
                        <td>{{ record.matric_number }}</td>
                        <td>{{ record.department }}</td>
                        <td>{{ record.timestamp }}</td>
                        <td>
                            {% if record.is_late %}
                            <span class="badge bg-warning">Late</span>
                            {% else %}
                            <span class="badge bg-success">On Time</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    {% if not attendance %}
                    <tr>
                        <td colspan="5" class="text-center">No attendance records found</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Attendance chart
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        const attendanceChart = new Chart(attendanceCtx, {
            type: 'pie',
            data: {
                labels: ['On Time', 'Late', 'Absent'],
                datasets: [{
                    data: [{{ on_time_count|default(0) }}, {{ late_count|default(0) }}, {{ absent_count|default(0) }}],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.5)',
                        'rgba(255, 206, 86, 0.5)',
                        'rgba(255, 99, 132, 0.5)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            }
        });
    });
</script>
{% endblock %}