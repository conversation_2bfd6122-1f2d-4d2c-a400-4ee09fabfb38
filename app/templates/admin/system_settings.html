{% extends "base.html" %}

{% block title %}System Settings - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">System Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-success" id="saveAllSettings">
            <i class="bi bi-save"></i> Save All Changes
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Attendance Settings</h5>
            </div>
            <div class="card-body">
                <form id="attendanceSettingsForm">
                    <input type="hidden" name="csrf_token" value="{{ custom_csrf_token }}">
                    <div class="mb-3">
                        <label for="gracePeriod" class="form-label">Late Grace Period (minutes)</label>
                        <input type="number" class="form-control" id="gracePeriod" name="late_threshold_minutes" 
                               value="{{ settings.late_threshold_minutes|default(15) }}" min="0" max="60">
                        <div class="form-text">Students arriving within this time after lecture start will be marked as on-time</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="attendanceThreshold" class="form-label">Attendance Eligibility Threshold (%)</label>
                        <input type="number" class="form-control" id="attendanceThreshold" name="attendance_threshold" 
                               value="{{ settings.attendance_threshold }}" min="0" max="100">
                        <div class="form-text">Minimum percentage of lecture duration required to be marked as present</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="exitTimeout" class="form-label">Exit Timeout (seconds)</label>
                        <input type="number" class="form-control" id="exitTimeout" name="exit_timeout_seconds" 
                               value="{{ settings.exit_timeout_seconds }}" min="1" max="300">
                        <div class="form-text">Time after which a student is considered to have left if not detected</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Save Attendance Settings</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Face Detection Settings</h5>
            </div>
            <div class="card-body">
                <form id="faceDetectionSettingsForm">
                    <input type="hidden" name="csrf_token" value="{{ custom_csrf_token }}">
                    <div class="mb-3">
                        <label for="detectionConfidence" class="form-label">Face Detection Confidence</label>
                        <input type="range" class="form-range" id="detectionConfidence" name="confidence_threshold" 
                               value="{{ settings.confidence_threshold|default(0.85) }}" min="0.1" max="0.9" step="0.05">
                        <div class="d-flex justify-content-between">
                            <span>Low (More Matches)</span>
                            <span id="confidenceValue">{{ settings.confidence_threshold|default(0.85) }}</span>
                            <span>High (Fewer Matches)</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="depthRangeMin" class="form-label">Depth Range Minimum (mm)</label>
                        <input type="number" class="form-control" id="depthRangeMin" name="depth_range_min" 
                               value="{{ settings.depth_range_mm[0] if settings.depth_range_mm is defined and settings.depth_range_mm is iterable else 500 }}" min="100" max="2000">
                    </div>
                    
                    <div class="mb-3">
                        <label for="depthRangeMax" class="form-label">Depth Range Maximum (mm)</label>
                        <input type="number" class="form-control" id="depthRangeMax" name="depth_range_max" 
                               value="{{ settings.depth_range_mm[1] if settings.depth_range_mm is defined and settings.depth_range_mm is iterable else 3000 }}" min="500" max="5000">
                    </div>
                    
                    <div class="mb-3">
                        <label for="irThreshold" class="form-label">IR Reflectance Threshold</label>
                        <input type="number" class="form-control" id="irThreshold" name="ir_reflectance_threshold" 
                               value="{{ settings.ir_reflectance_threshold|default(1000) }}" min="100" max="5000">
                    </div>
                    
                    <button type="submit" class="btn btn-info">Save Face Detection Settings</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Notification Settings</h5>
            </div>
            <div class="card-body">
                <form id="notificationSettingsForm">
                    <input type="hidden" name="csrf_token" value="{{ custom_csrf_token }}">
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="enableEmailNotifications" name="enable_email_notifications" 
                               {% if settings.enable_email_notifications %}checked{% endif %}>
                        <label class="form-check-label" for="enableEmailNotifications">Enable Email Notifications</label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="emailServer" class="form-label">SMTP Server</label>
                        <input type="text" class="form-control" id="emailServer" name="email_server" 
                               value="{{ settings.email_server|default('') }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="emailPort" class="form-label">SMTP Port</label>
                        <input type="number" class="form-control" id="emailPort" name="email_port" 
                               value="{{ settings.email_port|default('587') }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="emailUsername" class="form-label">Email Username</label>
                        <input type="text" class="form-control" id="emailUsername" name="email_username" 
                               value="{{ settings.email_username|default('') }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="emailPassword" class="form-label">Email Password</label>
                        <input type="password" class="form-control" id="emailPassword" name="email_password" 
                               value="{{ settings.email_password|default('') }}">
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notifyAbsences" name="notify_absences" 
                               {% if settings.notify_absences %}checked{% endif %}>
                        <label class="form-check-label" for="notifyAbsences">Notify Students of Absences</label>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notifySpoofing" name="notify_spoofing" 
                               {% if settings.notify_spoofing %}checked{% endif %}>
                        <label class="form-check-label" for="notifySpoofing">Notify Admins of Spoofing Attempts</label>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">Save Notification Settings</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">System Maintenance</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Database Backup</h6>
                    <p>Create a backup of the entire database including all attendance records, student data, and face encodings.</p>
                    <button id="backupDatabase" class="btn btn-secondary">
                        <i class="bi bi-download"></i> Backup Database
                    </button>
                </div>
                
                <div class="mb-4">
                    <h6>Import Face Encodings</h6>
                    <p>Import face encodings from the images directory to the database.</p>
                    <button id="importFaces" class="btn btn-secondary">
                        <i class="bi bi-person-bounding-box"></i> Import Face Encodings
                    </button>
                </div>
                
                <div class="mb-4">
                    <h6>Clear Attendance Logs</h6>
                    <p class="text-danger">Warning: This will permanently delete all attendance records older than the selected date.</p>
                    <div class="input-group mb-3">
                        <input type="date" class="form-control" id="clearLogsDate">
                        <button id="clearLogs" class="btn btn-danger">
                            <i class="bi bi-trash"></i> Clear Logs
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">SMS Notification Settings</h5>
            </div>
            <div class="card-body">
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="enableSmsNotifications" name="enable_sms_notifications" 
                           {% if settings.enable_sms_notifications %}checked{% endif %}>
                    <label class="form-check-label" for="enableSmsNotifications">Enable SMS Notifications</label>
                </div>
                
                <div class="mb-3">
                    <label for="smsProvider" class="form-label">SMS Provider</label>
                    <select class="form-select" id="smsProvider" name="sms_provider">
                        <option value="twilio" {% if settings.sms_provider == 'twilio' %}selected{% endif %}>Twilio</option>
                        <option value="whatsapp" {% if settings.sms_provider == 'whatsapp' %}selected{% endif %}>WhatsApp Business API</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="smsApiKey" class="form-label">API Key/Account SID</label>
                    <input type="text" class="form-control" id="smsApiKey" name="sms_api_key" 
                           value="{{ settings.sms_api_key|default('') }}">
                </div>
                
                <div class="mb-3">
                    <label for="smsApiSecret" class="form-label">API Secret/Auth Token</label>
                    <input type="password" class="form-control" id="smsApiSecret" name="sms_api_secret" 
                           value="{{ settings.sms_api_secret|default('') }}">
                </div>
                
                <div class="mb-3">
                    <label for="smsFromNumber" class="form-label">From Phone Number</label>
                    <input type="text" class="form-control" id="smsFromNumber" name="sms_from_number" 
                           value="{{ settings.sms_from_number|default('') }}">
                    <div class="form-text">For Twilio, use the format +**********. For WhatsApp, use the format whatsapp:+**********</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update confidence value display
        const confidenceSlider = document.getElementById('detectionConfidence');
        const confidenceValue = document.getElementById('confidenceValue');
        
        confidenceSlider.addEventListener('input', function() {
            confidenceValue.textContent = this.value;
        });
        
        // Handle individual form submissions
        document.getElementById('attendanceSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('attendance', this);
        });
        
        document.getElementById('faceDetectionSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('face_detection', this);
        });
        
        document.getElementById('notificationSettingsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveSettings('notification', this);
        });
        
        // Save all settings at once
        document.getElementById('saveAllSettings').addEventListener('click', function() {
            saveSettings('all');
        });
        
        // Database backup
        document.getElementById('backupDatabase').addEventListener('click', function() {
            fetch('/admin/backup_database', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                }
            })
            .then(response => response.blob())
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'biometric_attendance_backup.db';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                showAlert('Database backup created successfully', 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Failed to create database backup', 'danger');
            });
        });
        
        // Import faces
        document.getElementById('importFaces').addEventListener('click', function() {
            fetch('/admin/import_faces', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Face encodings imported successfully', 'success');
                } else {
                    showAlert('Failed to import face encodings: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Failed to import face encodings', 'danger');
            });
        });
        
        // Clear logs
        document.getElementById('clearLogs').addEventListener('click', function() {
            const date = document.getElementById('clearLogsDate').value;
            if (!date) {
                showAlert('Please select a date', 'warning');
                return;
            }
            
            if (confirm('Are you sure you want to delete all attendance records before ' + date + '? This action cannot be undone.')) {
                fetch('/admin/clear_logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                    },
                    body: JSON.stringify({ date: date })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('Attendance logs cleared successfully', 'success');
                    } else {
                        showAlert('Failed to clear logs: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Failed to clear logs', 'danger');
                });
            }
        });
        
        // Helper function to save settings
        function saveSettings(type, form) {
            let formData;
            
            if (type === 'all') {
                // Collect data from all forms
                const attendanceForm = new FormData(document.getElementById('attendanceSettingsForm'));
                const faceDetectionForm = new FormData(document.getElementById('faceDetectionSettingsForm'));
                const notificationForm = new FormData(document.getElementById('notificationSettingsForm'));
                
                formData = new FormData();
                
                // Combine all form data
                for (let pair of attendanceForm.entries()) {
                    formData.append(pair[0], pair[1]);
                }
                
                for (let pair of faceDetectionForm.entries()) {
                    formData.append(pair[0], pair[1]);
                }
                
                for (let pair of notificationForm.entries()) {
                    formData.append(pair[0], pair[1]);
                }
            } else {
                formData = new FormData(form);
            }
            
            // Convert FormData to JSON
            const jsonData = {};
            formData.forEach((value, key) => {
                if (key !== 'csrf_token') {
                    jsonData[key] = value;
                }
            });
            
            // Send data to server
            fetch('/admin/save_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                },
                body: JSON.stringify({
                    type: type,
                    settings: jsonData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Settings saved successfully', 'success');
                } else {
                    showAlert('Failed to save settings: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Failed to save settings', 'danger');
            });
        }
        
        // Helper function to show alerts
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            // Insert at the top of the content
            const content = document.querySelector('.container');
            content.insertBefore(alertDiv, content.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => alertDiv.remove(), 150);
            }, 5000);
        }
    });
</script>
{% endblock %}
