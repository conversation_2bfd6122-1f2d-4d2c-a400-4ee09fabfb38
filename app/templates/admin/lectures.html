{% extends "base.html" %}

{% block title %}Manage Lectures - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Manage Lectures</h1>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>ID</th>
                <th>Course</th>
                <th>Lecturer</th>
                <th>Date & Time</th>
                <th>Duration (mins)</th>
                <th>Attendance Count</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for lecture in lectures %}
            <tr>
                <td>{{ lecture.id }}</td>
                <td>{{ lecture.course }}</td>
                <td>{{ lecture.lecturer_name }}</td>
                <td>{{ lecture.start_time }}</td>
                <td>{{ lecture.duration_minutes }}</td>
                <td>{{ lecture.attendance_count }}</td>
                <td>
                    <a href="{{ url_for('admin_view_lecture_attendance', lecture_id=lecture.id) }}" class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i> View
                    </a>
                    <a href="{{ url_for('admin_export_lecture', lecture_id=lecture.id) }}" class="btn btn-sm btn-success">
                        <i class="bi bi-download"></i> Export
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}