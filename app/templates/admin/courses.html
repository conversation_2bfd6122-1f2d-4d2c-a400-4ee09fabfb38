{% extends "base.html" %}

{% block title %}Manage Courses - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Manage Courses</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
            <i class="bi bi-plus"></i> Add Course
        </button>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>Code</th>
                <th>Title</th>
                <th>Department</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for course in courses %}
            <tr>
                <td>{{ course.code }}</td>
                <td>{{ course.title }}</td>
                <td>{{ course.department }}</td>
                <td>
                    <a href="{{ url_for('admin_course_lecturers', course_id=course.id) }}" class="btn btn-sm btn-info">
                        <i class="bi bi-people"></i> Lecturers
                    </a>
                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editCourseModal{{ course.id }}">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <a href="{{ url_for('admin_delete_course', course_id=course.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this course?')">
                        <i class="bi bi-trash"></i> Delete
                    </a>
                </td>
            </tr>
            
            <!-- Edit Course Modal -->
            <div class="modal fade" id="editCourseModal{{ course.id }}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit Course</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ url_for('admin_edit_course', course_id=course.id) }}" method="post">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="code" class="form-label">Course Code</label>
                                    <input type="text" class="form-control" id="code" name="code" value="{{ course.code }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="title" class="form-label">Course Title</label>
                                    <input type="text" class="form-control" id="title" name="title" value="{{ course.title }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="department" class="form-label">Department</label>
                                    <input type="text" class="form-control" id="department" name="department" value="{{ course.department }}">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {% endfor %}
            
            {% if not courses %}
            <tr>
                <td colspan="4" class="text-center">No courses found</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Add Course Modal -->
<div class="modal fade" id="addCourseModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Course</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_add_course') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="code" class="form-label">Course Code</label>
                        <input type="text" class="form-control" id="code" name="code" placeholder="e.g., CMP411" required>
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Course Title</label>
                        <input type="text" class="form-control" id="title" name="title" placeholder="e.g., Advanced Programming" required>
                    </div>
                    <div class="mb-3">
                        <label for="department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department" name="department" placeholder="e.g., Computer Science">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Course</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}