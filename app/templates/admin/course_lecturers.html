{% extends "base.html" %}

{% block title %}Course Lecturers - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Manage Lecturers for {{ course.code }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin_courses') }}" class="btn btn-sm btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Courses
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Course Details</h5>
            </div>
            <div class="card-body">
                <p><strong>Code:</strong> {{ course.code }}</p>
                <p><strong>Title:</strong> {{ course.title }}</p>
                <p><strong>Department:</strong> {{ course.department }}</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Assign New Lecturer</h5>
            </div>
            <div class="card-body">
                {% if available_lecturers %}
                <form action="{{ url_for('admin_assign_lecturer', course_id=course.id) }}" method="post">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="lecturer_id" class="form-label">Select Lecturer</label>
                        <select class="form-select" id="lecturer_id" name="lecturer_id" required>
                            <option value="">-- Select Lecturer --</option>
                            {% for lecturer in available_lecturers %}
                            <option value="{{ lecturer.id }}">{{ lecturer.name }} ({{ lecturer.staff_id }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-plus-circle"></i> Assign Lecturer
                    </button>
                </form>
                {% else %}
                <p class="text-center">All lecturers are already assigned to this course.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Assigned Lecturers</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Staff ID</th>
                        <th>Department</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for lecturer in assigned_lecturers %}
                    <tr>
                        <td>{{ lecturer.name }}</td>
                        <td>{{ lecturer.staff_id }}</td>
                        <td>{{ lecturer.department }}</td>
                        <td>
                            <form action="{{ url_for('admin_remove_lecturer', course_id=course.id, lecturer_id=lecturer.id) }}" method="post" style="display: inline;">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to remove this lecturer from the course?')">
                                    <i class="bi bi-trash"></i> Remove
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                    {% if not assigned_lecturers %}
                    <tr>
                        <td colspan="4" class="text-center">No lecturers assigned to this course</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}