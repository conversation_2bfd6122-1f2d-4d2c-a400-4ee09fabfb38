{% extends "base.html" %}

{% block title %}Manage Lecturers - Biometric Attendance System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Manage Lecturers</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-sm btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addLecturerModal">
            <i class="bi bi-plus"></i> Add Lecturer
        </button>
        <button id="importLecturerFacesBtn" class="btn btn-sm btn-secondary">
            <i class="bi bi-person-bounding-box"></i> Import Face Encodings
        </button>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Staff ID</th>
                <th>Department</th>
                <th>Face Registered</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for lecturer in lecturers %}
            <tr>
                <td>{{ lecturer.id }}</td>
                <td>{{ lecturer.name }}</td>
                <td>{{ lecturer.staff_id }}</td>
                <td>{{ lecturer.department }}</td>
                <td>
                    {% if lecturer.has_face %}
                    <span class="badge bg-success">Yes</span>
                    {% else %}
                    <span class="badge bg-danger">No</span>
                    {% endif %}
                </td>
                <td>
                    <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewLecturerModal{{ lecturer.id }}">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editLecturerModal{{ lecturer.id }}">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteLecturerModal{{ lecturer.id }}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Add Lecturer Modal -->
<div class="modal fade" id="addLecturerModal" tabindex="-1" aria-labelledby="addLecturerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addLecturerModalLabel">Add New Lecturer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addLecturerForm" method="post" action="{{ url_for('admin_add_lecturer') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="staff_id" class="form-label">Staff ID</label>
                        <input type="text" class="form-control" id="staff_id" name="staff_id" required>
                    </div>
                    <div class="mb-3">
                        <label for="department" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department" name="department" required>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="addLecturerForm" class="btn btn-primary">Add Lecturer</button>
            </div>
        </div>
    </div>
</div>

<!-- View/Edit/Delete Modals -->
{% for lecturer in lecturers %}
<!-- View Lecturer Modal -->
<div class="modal fade" id="viewLecturerModal{{ lecturer.id }}" tabindex="-1" aria-labelledby="viewLecturerModalLabel{{ lecturer.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewLecturerModalLabel{{ lecturer.id }}">Lecturer Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Name:</strong> {{ lecturer.name }}
                </div>
                <div class="mb-3">
                    <strong>Staff ID:</strong> {{ lecturer.staff_id }}
                </div>
                <div class="mb-3">
                    <strong>Department:</strong> {{ lecturer.department }}
                </div>
                <div class="mb-3">
                    <strong>Username:</strong> 
                    {% if lecturer.username %}
                        {{ lecturer.username }}
                    {% else %}
                        <span class="text-muted">Not set</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>Email:</strong> 
                    {% if lecturer.email %}
                        {{ lecturer.email }}
                    {% else %}
                        <span class="text-muted">Not set</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>Phone:</strong> 
                    {% if lecturer.phone %}
                        {{ lecturer.phone }}
                    {% else %}
                        <span class="text-muted">Not set</span>
                    {% endif %}
                </div>
                <div class="mb-3">
                    <strong>Face Registered:</strong> 
                    {% if lecturer.has_face %}
                    <span class="badge bg-success">Yes</span>
                    {% else %}
                    <span class="badge bg-danger">No</span>
                    {% endif %}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Lecturer Modal -->
<div class="modal fade" id="editLecturerModal{{ lecturer.id }}" tabindex="-1" aria-labelledby="editLecturerModalLabel{{ lecturer.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editLecturerModalLabel{{ lecturer.id }}">Edit Lecturer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editLecturerForm{{ lecturer.id }}" method="post" action="{{ url_for('admin_edit_lecturer', lecturer_id=lecturer.id) }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name{{ lecturer.id }}" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="name{{ lecturer.id }}" name="name" value="{{ lecturer.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="staff_id{{ lecturer.id }}" class="form-label">Staff ID</label>
                        <input type="text" class="form-control" id="staff_id{{ lecturer.id }}" name="staff_id" value="{{ lecturer.staff_id }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="department{{ lecturer.id }}" class="form-label">Department</label>
                        <input type="text" class="form-control" id="department{{ lecturer.id }}" name="department" value="{{ lecturer.department }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="username{{ lecturer.id }}" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username{{ lecturer.id }}" name="username" value="{{ lecturer.username }}" {% if lecturer.username %}readonly{% endif %}>
                        {% if lecturer.username %}
                        <div class="form-text">Username cannot be changed once set.</div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="password{{ lecturer.id }}" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password{{ lecturer.id }}" name="password" placeholder="Leave blank to keep current password">
                        <div class="form-text">Enter a new password only if you want to change it.</div>
                    </div>
                    <div class="mb-3">
                        <label for="email{{ lecturer.id }}" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email{{ lecturer.id }}" name="email" value="{{ lecturer.email }}">
                    </div>
                    <div class="mb-3">
                        <label for="phone{{ lecturer.id }}" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone{{ lecturer.id }}" name="phone" value="{{ lecturer.phone }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editLecturerForm{{ lecturer.id }}" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Lecturer Modal -->
<div class="modal fade" id="deleteLecturerModal{{ lecturer.id }}" tabindex="-1" aria-labelledby="deleteLecturerModalLabel{{ lecturer.id }}" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteLecturerModalLabel{{ lecturer.id }}">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete lecturer <strong>{{ lecturer.name }}</strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="{{ url_for('admin_delete_lecturer', lecturer_id=lecturer.id) }}" class="btn btn-danger">Delete Lecturer</a>
            </div>
        </div>
    </div>
</div>
{% endfor %}

{% endblock %}

<script>
document.getElementById('importLecturerFacesBtn').addEventListener('click', function() {
    if (confirm('Import lecturer face encodings from the images directory? This may take a few moments.')) {
        fetch('/admin/import_faces', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Face encodings imported successfully!');
                location.reload(); // Reload to show updated face encoding status
            } else {
                alert('Error importing face encodings: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while importing face encodings.');
        });
    }
});
</script>
