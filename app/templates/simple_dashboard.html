{% extends "base.html" %}

{% block title %}Simple Dashboard{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Simple Dashboard</h4>
                </div>
                <div class="card-body">
                    <h5>User Information</h5>
                    <ul>
                        <li><strong>ID:</strong> {{ user.id }}</li>
                        <li><strong>Username:</strong> {{ user.username }}</li>
                        <li><strong>Role:</strong> {{ user.role }}</li>
                        <li><strong>Is Authenticated:</strong> {{ user.is_authenticated }}</li>
                    </ul>
                    
                    <h5>Session Data</h5>
                    <ul>
                        {% for key, value in session_data.items() %}
                        <li><strong>{{ key }}:</strong> {{ value }}</li>
                        {% endfor %}
                    </ul>
                    
                    <div class="mt-4">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">Go to Main Dashboard</a>
                        <a href="{{ url_for('logout') }}" class="btn btn-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}