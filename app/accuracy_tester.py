#!/usr/bin/env python3
"""
Accuracy and Effectiveness Testing System
Tests the accuracy of face recognition and attendance marking
"""

import os
import sys
import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime, timedelta
import face_recognition
from collections import defaultdict, Counter

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from recognizer import load_known_faces
from attendance import process_frame, init_attendance_session
from db import DB_PATH, get_all_students, create_lecture, get_lecture_attendance
from logger import log_system_event

class AccuracyTester:
    """Comprehensive accuracy testing for the attendance system"""
    
    def __init__(self):
        self.results = {
            'face_recognition': {},
            'multi_face': {},
            'liveness_detection': {},
            'attendance_accuracy': {},
            'system_performance': {}
        }
        
    def test_face_recognition_accuracy(self):
        """Test face recognition accuracy using known images"""
        print("🔍 Testing Face Recognition Accuracy...")
        
        try:
            # Load known faces
            encodings, names, student_map = load_known_faces(use_database=True)
            
            if not encodings:
                print("❌ No face encodings found")
                return False
                
            print(f"✅ Loaded {len(encodings)} face encodings")
            
            # Test with images from the images folder
            images_dir = "images/student_faces"
            if not os.path.exists(images_dir):
                print("❌ Images directory not found")
                return False
                
            correct_recognitions = 0
            total_tests = 0
            false_positives = 0
            false_negatives = 0
            
            recognition_scores = []
            
            for student_folder in os.listdir(images_dir):
                folder_path = os.path.join(images_dir, student_folder)
                if not os.path.isdir(folder_path):
                    continue
                    
                expected_name = student_folder.strip().lower().replace("_", " ")
                
                for image_file in os.listdir(folder_path):
                    if not image_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        continue
                        
                    image_path = os.path.join(folder_path, image_file)
                    image = cv2.imread(image_path)
                    
                    if image is None:
                        continue
                        
                    total_tests += 1
                    
                    # Convert to RGB
                    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # Detect faces
                    face_locations = face_recognition.face_locations(rgb_image)
                    
                    if len(face_locations) == 0:
                        false_negatives += 1
                        print(f"❌ No face detected in {image_file}")
                        continue
                        
                    if len(face_locations) > 1:
                        print(f"⚠️ Multiple faces in {image_file}, using first")
                        
                    # Get face encoding
                    face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
                    
                    if not face_encodings:
                        false_negatives += 1
                        continue
                        
                    face_encoding = face_encodings[0]
                    
                    # Compare with known faces
                    if encodings:
                        face_distances = face_recognition.face_distance(encodings, face_encoding)
                        best_match_index = np.argmin(face_distances)
                        best_distance = face_distances[best_match_index]
                        
                        # Use same threshold as in attendance.py
                        threshold = 1 - 0.6  # DETECTION_CONFIDENCE = 0.6
                        
                        if best_distance < threshold:
                            recognized_name = names[best_match_index]
                            
                            if recognized_name == expected_name:
                                correct_recognitions += 1
                                recognition_scores.append(1 - best_distance)  # Convert to confidence
                                print(f"✅ {image_file}: Correctly recognized as {recognized_name} (distance: {best_distance:.3f})")
                            else:
                                false_positives += 1
                                print(f"❌ {image_file}: Incorrectly recognized as {recognized_name}, expected {expected_name}")
                        else:
                            false_negatives += 1
                            print(f"❌ {image_file}: Not recognized (distance: {best_distance:.3f})")
                    else:
                        false_negatives += 1
                        
                    # Limit tests for performance
                    if total_tests >= 50:
                        break
                        
                if total_tests >= 50:
                    break
                    
            # Calculate metrics
            accuracy = correct_recognitions / total_tests if total_tests > 0 else 0
            precision = correct_recognitions / (correct_recognitions + false_positives) if (correct_recognitions + false_positives) > 0 else 0
            recall = correct_recognitions / (correct_recognitions + false_negatives) if (correct_recognitions + false_negatives) > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            avg_confidence = np.mean(recognition_scores) if recognition_scores else 0
            
            self.results['face_recognition'] = {
                'total_tests': total_tests,
                'correct_recognitions': correct_recognitions,
                'false_positives': false_positives,
                'false_negatives': false_negatives,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1_score,
                'average_confidence': avg_confidence
            }
            
            print(f"\n📊 Face Recognition Results:")
            print(f"   Total Tests: {total_tests}")
            print(f"   Correct: {correct_recognitions}")
            print(f"   False Positives: {false_positives}")
            print(f"   False Negatives: {false_negatives}")
            print(f"   Accuracy: {accuracy:.2%}")
            print(f"   Precision: {precision:.2%}")
            print(f"   Recall: {recall:.2%}")
            print(f"   F1 Score: {f1_score:.2%}")
            print(f"   Avg Confidence: {avg_confidence:.2%}")
            
            return accuracy > 0.8  # 80% accuracy threshold
            
        except Exception as e:
            print(f"❌ Face recognition test failed: {e}")
            return False
            
    def test_multi_face_detection(self):
        """Test multi-face detection capability"""
        print("\n🔍 Testing Multi-Face Detection...")
        
        try:
            # Create test images with multiple faces
            test_results = []
            
            # Test 1: Process images with multiple faces if available
            images_dir = "images/student_faces"
            multi_face_images = []
            
            # Look for images that might contain multiple faces
            for student_folder in os.listdir(images_dir)[:5]:  # Test first 5 students
                folder_path = os.path.join(images_dir, student_folder)
                if not os.path.isdir(folder_path):
                    continue
                    
                for image_file in os.listdir(folder_path):
                    if image_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        image_path = os.path.join(folder_path, image_file)
                        image = cv2.imread(image_path)
                        
                        if image is not None:
                            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                            face_locations = face_recognition.face_locations(rgb_image)
                            
                            if len(face_locations) > 1:
                                multi_face_images.append((image_path, len(face_locations)))
                                print(f"📷 Found {len(face_locations)} faces in {image_file}")
                                
            # Test 2: Simulate multi-face scenario
            if not multi_face_images:
                print("⚠️ No multi-face images found, creating synthetic test")
                
                # Create a synthetic multi-face test
                test_image = np.zeros((480, 640, 3), dtype=np.uint8)
                test_ir = np.ones((424, 512), dtype=np.uint16) * 1500
                test_depth = np.ones((424, 512), dtype=np.uint16) * 1000
                
                # Test the process_frame function
                processed_frame = process_frame(test_image, test_ir, test_depth, camera_id=0)
                
                if processed_frame is not None:
                    test_results.append(True)
                    print("✅ Multi-face processing function works")
                else:
                    test_results.append(False)
                    print("❌ Multi-face processing failed")
            else:
                # Test actual multi-face images
                for image_path, face_count in multi_face_images[:3]:  # Test first 3
                    image = cv2.imread(image_path)
                    rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    
                    # Create dummy IR and depth frames
                    test_ir = np.ones((424, 512), dtype=np.uint16) * 1500
                    test_depth = np.ones((424, 512), dtype=np.uint16) * 1000
                    
                    processed_frame = process_frame(rgb_image, test_ir, test_depth, camera_id=0)
                    
                    if processed_frame is not None:
                        test_results.append(True)
                        print(f"✅ Successfully processed {face_count} faces in {os.path.basename(image_path)}")
                    else:
                        test_results.append(False)
                        print(f"❌ Failed to process {os.path.basename(image_path)}")
                        
            success_rate = sum(test_results) / len(test_results) if test_results else 0
            
            self.results['multi_face'] = {
                'tests_performed': len(test_results),
                'successful_tests': sum(test_results),
                'success_rate': success_rate,
                'multi_face_images_found': len(multi_face_images)
            }
            
            print(f"\n📊 Multi-Face Detection Results:")
            print(f"   Tests Performed: {len(test_results)}")
            print(f"   Successful: {sum(test_results)}")
            print(f"   Success Rate: {success_rate:.2%}")
            print(f"   Multi-face Images Found: {len(multi_face_images)}")
            
            return success_rate > 0.8
            
        except Exception as e:
            print(f"❌ Multi-face detection test failed: {e}")
            return False
            
    def test_attendance_accuracy(self):
        """Test end-to-end attendance accuracy"""
        print("\n🔍 Testing Attendance Accuracy...")
        
        try:
            # Create a test lecture
            with sqlite3.connect(DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                c = conn.cursor()
                
                # Get first course and lecturer
                c.execute("SELECT id FROM courses LIMIT 1")
                course = c.fetchone()
                c.execute("SELECT id FROM lecturers LIMIT 1")
                lecturer = c.fetchone()
                
                if not course or not lecturer:
                    print("❌ No courses or lecturers found")
                    return False
                    
                # Create test lecture
                from db import create_lecture
                lecture_id = create_lecture(
                    course_id=course['id'],
                    lecturer_id=lecturer['id'],
                    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    duration=60,
                    room="Test Room"
                )
                
                if not lecture_id:
                    print("❌ Failed to create test lecture")
                    return False
                    
                print(f"✅ Created test lecture: {lecture_id}")
                
                # Initialize attendance session
                success = init_attendance_session(lecture_id)
                
                if not success:
                    print("❌ Failed to initialize attendance session")
                    return False
                    
                print("✅ Attendance session initialized")
                
                # Simulate attendance marking
                attendance_tests = []
                
                # Get some students
                students = get_all_students()[:5]  # Test first 5 students
                
                for student in students:
                    if student['has_face']:
                        # Simulate successful recognition and attendance marking
                        try:
                            from db import record_student_attendance
                            result = record_student_attendance(
                                student_id=student['id'],
                                lecture_id=lecture_id,
                                is_late=False,
                                confidence=0.85
                            )
                            
                            if result:
                                attendance_tests.append(True)
                                print(f"✅ Recorded attendance for {student['name']}")
                            else:
                                attendance_tests.append(False)
                                print(f"❌ Failed to record attendance for {student['name']}")
                                
                        except Exception as e:
                            attendance_tests.append(False)
                            print(f"❌ Error recording attendance for {student['name']}: {e}")
                            
                # Check recorded attendance
                recorded_attendance = get_lecture_attendance(lecture_id)
                
                success_rate = sum(attendance_tests) / len(attendance_tests) if attendance_tests else 0
                
                self.results['attendance_accuracy'] = {
                    'students_tested': len(attendance_tests),
                    'successful_recordings': sum(attendance_tests),
                    'success_rate': success_rate,
                    'recorded_attendance_count': len(recorded_attendance)
                }
                
                print(f"\n📊 Attendance Accuracy Results:")
                print(f"   Students Tested: {len(attendance_tests)}")
                print(f"   Successful Recordings: {sum(attendance_tests)}")
                print(f"   Success Rate: {success_rate:.2%}")
                print(f"   Recorded in Database: {len(recorded_attendance)}")
                
                return success_rate > 0.8
                
        except Exception as e:
            print(f"❌ Attendance accuracy test failed: {e}")
            return False
            
    def generate_accuracy_report(self):
        """Generate comprehensive accuracy report"""
        print("\n" + "="*60)
        print("📋 COMPREHENSIVE ACCURACY REPORT")
        print("="*60)
        
        # Overall system score
        scores = []
        
        # Face Recognition Score
        if 'face_recognition' in self.results and self.results['face_recognition']:
            fr_score = self.results['face_recognition']['f1_score']
            scores.append(fr_score)
            print(f"\n🎯 Face Recognition Performance:")
            print(f"   F1 Score: {fr_score:.2%}")
            print(f"   Accuracy: {self.results['face_recognition']['accuracy']:.2%}")
            print(f"   Precision: {self.results['face_recognition']['precision']:.2%}")
            print(f"   Recall: {self.results['face_recognition']['recall']:.2%}")
            
        # Multi-Face Detection Score
        if 'multi_face' in self.results and self.results['multi_face']:
            mf_score = self.results['multi_face']['success_rate']
            scores.append(mf_score)
            print(f"\n🎯 Multi-Face Detection Performance:")
            print(f"   Success Rate: {mf_score:.2%}")
            
        # Attendance Accuracy Score
        if 'attendance_accuracy' in self.results and self.results['attendance_accuracy']:
            att_score = self.results['attendance_accuracy']['success_rate']
            scores.append(att_score)
            print(f"\n🎯 Attendance Recording Performance:")
            print(f"   Success Rate: {att_score:.2%}")
            
        # Overall System Score
        if scores:
            overall_score = np.mean(scores)
            print(f"\n🏆 OVERALL SYSTEM SCORE: {overall_score:.2%}")
            
            if overall_score >= 0.9:
                grade = "A+ (Excellent)"
                color = "🟢"
            elif overall_score >= 0.8:
                grade = "A (Very Good)"
                color = "🟢"
            elif overall_score >= 0.7:
                grade = "B (Good)"
                color = "🟡"
            elif overall_score >= 0.6:
                grade = "C (Fair)"
                color = "🟡"
            else:
                grade = "D (Needs Improvement)"
                color = "🔴"
                
            print(f"{color} System Grade: {grade}")
            
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if 'face_recognition' in self.results:
            fr = self.results['face_recognition']
            if fr.get('accuracy', 0) < 0.8:
                print("   • Improve face recognition by adding more training images per student")
                print("   • Consider adjusting the confidence threshold")
                
        if 'multi_face' in self.results:
            mf = self.results['multi_face']
            if mf.get('success_rate', 0) < 0.8:
                print("   • Test with more multi-face scenarios")
                print("   • Optimize face detection parameters")
                
        if 'attendance_accuracy' in self.results:
            att = self.results['attendance_accuracy']
            if att.get('success_rate', 0) < 0.8:
                print("   • Check database connectivity and permissions")
                print("   • Verify student enrollment in courses")
                
        # Save report to file
        report_file = f"accuracy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
            
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        return overall_score if scores else 0
        
    def run_all_tests(self):
        """Run all accuracy tests"""
        print("🚀 Starting Comprehensive Accuracy Testing")
        print("="*60)
        
        # Test 1: Face Recognition Accuracy
        fr_success = self.test_face_recognition_accuracy()
        
        # Test 2: Multi-Face Detection
        mf_success = self.test_multi_face_detection()
        
        # Test 3: Attendance Accuracy
        att_success = self.test_attendance_accuracy()
        
        # Generate report
        overall_score = self.generate_accuracy_report()
        
        return overall_score


def main():
    """Main function to run accuracy tests"""
    tester = AccuracyTester()
    overall_score = tester.run_all_tests()
    
    if overall_score >= 0.8:
        print("\n🎉 Your attendance system shows excellent accuracy!")
        return True
    else:
        print("\n⚠️ Your attendance system needs improvement.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
