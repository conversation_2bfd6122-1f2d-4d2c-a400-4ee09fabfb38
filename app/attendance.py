import os
import csv
from datetime import datetime, timedelta
from liveness import is_live_face, is_masked_face, prune_face_history
import face_recognition
import cv2
import numpy as np
from logger import log_entry, log_exit, log_system_event, log_spoofing_attempt
from db import get_lecture_by_id, record_student_attendance, get_students_enrolled_in_course
from recognizer import load_known_faces

# === CONFIGURATION ===
EXIT_TIMEOUT_SECONDS = 5             # If unseen for this time, mark as exit
LECTURE_DURATION_MINUTES = 60        # Total lecture time
DETECTION_CONFIDENCE = 0.6           # Optional threshold for face matching
END_LECTURE_KEY = ord('q')           # Press 'q' to end lecture manually

# === STATE ===
entry_logged = {}
exit_logged = {}
last_seen = {}
attendance_records = {}
session_start = datetime.now()
seen_now = set()
lecture_ended = False  # 💡 New state to track if lecture ended

# Add global variables for lecture tracking
current_lecture_id = None
current_lecture_info = None
known_encodings = []
known_names = []
student_id_map = {}  # Maps student names to their database IDs

def init_attendance_session(lec_id):
    """Initialize the attendance session with the given lecture ID"""
    global current_lecture_id, current_lecture_info, session_start, known_encodings, known_names, student_id_map

    current_lecture_id = lec_id
    current_lecture_info = get_lecture_by_id(current_lecture_id)

    if not current_lecture_info:
        log_system_event(f"Lecture with ID {current_lecture_id} not found", "ERROR")
        return False

    # Reset attendance tracking
    session_start = datetime.now()
    entry_logged.clear()
    exit_logged.clear()
    last_seen.clear()
    attendance_records.clear()
    seen_now.clear()

    # Load student faces for this course
    try:
        # Get students enrolled in this course
        students = get_students_enrolled_in_course(current_lecture_info['course_id'])

        # Load face encodings for these students (corrected function call)
        known_encodings, known_names, student_id_map = load_known_faces(
            student_ids=[s['id'] for s in students] if students else None,
            use_database=True
        )

        log_system_event(f"Attendance session initialized for lecture ID {current_lecture_id}", "INFO")
        log_system_event(f"Course: {current_lecture_info.get('course_name', 'Unknown')}", "INFO")
        log_system_event(f"Loaded {len(known_encodings)} student faces", "INFO")
        log_system_event(f"Students enrolled: {[s['name'] for s in students] if students else []}", "INFO")

        return True
    except Exception as e:
        log_system_event(f"Error initializing attendance session: {e}", "ERROR")
        return False

def update_attendance(name, current_time, camera_id=0):
    if name in last_seen:
        delta = (current_time - last_seen[name]).total_seconds()
        if delta < 3:  # 💡 Less than 3 seconds since last seen = probably duplicate
            return  # Skip duplicate from another Kinect

    seen_now.add(name)

    if name not in entry_logged or exit_logged.get(name) is not None:
        entry_logged[name] = current_time
        exit_logged[name] = None
        attendance_records.setdefault(name, [])
        log_entry(name, camera_id)
        log_system_event(f"[✅ ENTRY] {name} at {current_time.strftime('%H:%M:%S')} via camera {camera_id}")

    last_seen[name] = current_time
    # When updating attendance, you might want to record it in the database
    # if current_lecture_id is set:
    if name in known_names and current_lecture_id is not None:
        # Get student ID from name
        student_index = known_names.index(name)

        # Record attendance in database
        try:
            record_student_attendance(name, current_lecture_id)
        except Exception as e:
            log_system_event(f"Error recording attendance: {e}", "ERROR")



def handle_exit_check():
    """Mark exit if a student has not been seen for a while."""
    now = datetime.now()
    for name in list(last_seen.keys()):
        if name not in seen_now:
            diff = (now - last_seen[name]).total_seconds()
            if diff > EXIT_TIMEOUT_SECONDS:
                if name in entry_logged and exit_logged.get(name) is None:
                    exit_logged[name] = last_seen[name]
                    attendance_records[name].append((entry_logged[name], exit_logged[name]))
                    print(f"[🚪 EXIT] {name} at {exit_logged[name].strftime('%H:%M:%S')}")


def reset_seen_now():
    seen_now.clear()
    prune_face_history()


def finalize_attendance():
    """Force mark all present students as exited when lecture ends."""
    session_end = datetime.now()
    for name in entry_logged:
        if name not in exit_logged or exit_logged[name] is None:
            exit_logged[name] = session_end
            attendance_records.setdefault(name, []).append((entry_logged[name], session_end))
            print(f"[FORCED EXIT] {name} at {session_end.strftime('%H:%M:%S')}")
    return attendance_records, session_start, session_end


def save_attendance(attendance_records, session_start, session_end, lecture_duration_minutes):
    if not os.path.exists("attendance_logs"):
        os.makedirs("attendance_logs")

    filename = f"attendance_logs/attendance_{session_start.strftime('%Y-%m-%d_%H-%M-%S')}.csv"
    with open(filename, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(["Student Name", "Entry Time", "Exit Time", "Duration (mins)", "Eligible (>=70%)"])

        for name, sessions in attendance_records.items():
            total_minutes = 0
            for entry, exit in sessions:
                duration = (exit - entry).total_seconds() / 60
                total_minutes += duration

                writer.writerow([
                    name,
                    entry.strftime("%H:%M:%S"),
                    exit.strftime("%H:%M:%S"),
                    f"{duration:.2f}",
                    ""
                ])

            eligible = total_minutes >= (0.7 * lecture_duration_minutes)
            writer.writerow([
                f"{name}", "", "", f"{total_minutes:.2f}",
                "Yes" if eligible else "No"
            ])

    print(f"✅ Attendance saved to {filename}")


# Optimize face detection for speed
def process_frame(rgb_frame, ir_frame, depth_frame, camera_id=0):
    """Process a frame from any Kinect camera to detect and log attendance."""
    global current_lecture_id

    # Skip if no lecture is active
    if current_lecture_id is None:
        return rgb_frame

    current_time = datetime.now()

    # Create a copy of the frame to avoid modifying the original
    display_frame = rgb_frame.copy() if rgb_frame is not None else None

    # Use a smaller scale factor for faster face detection
    # Original scale is typically 1.3, using 1.1 will be faster
    face_locations = face_recognition.face_locations(rgb_frame, model="hog")  # Use HOG for speed

    # Only compute encodings if faces are found (saves processing time)
    if face_locations:
        encodings = face_recognition.face_encodings(rgb_frame, face_locations, num_jitters=0)  # No jitters for speed
    else:
        encodings = []

    # Skip IR fallback for speed unless no faces found in RGB
    if len(face_locations) == 0 and ir_frame is not None:
        # Downsample IR for faster processing
        ir_small = cv2.resize(ir_frame, (0, 0), fx=0.5, fy=0.5)
        ir_normalized = cv2.normalize(ir_small, None, 0, 255, cv2.NORM_MINMAX)
        ir_normalized = np.uint8(ir_normalized)
        ir_rgb = cv2.cvtColor(ir_normalized, cv2.COLOR_GRAY2RGB)

        face_locations = face_recognition.face_locations(ir_rgb, model="hog")

        # Scale locations back up
        face_locations = [(top*2, right*2, bottom*2, left*2) for top, right, bottom, left in face_locations]

        if face_locations:
            encodings = face_recognition.face_encodings(rgb_frame, face_locations, num_jitters=0)
        else:
            encodings = []

    for encoding, bbox in zip(encodings, face_locations):
        name = "Unknown"

        # Only compute face distances if we have known encodings
        if len(known_encodings) > 0:
            face_distances = face_recognition.face_distance(known_encodings, encoding)
            best_match_index = np.argmin(face_distances)

            if face_distances[best_match_index] < (1 - DETECTION_CONFIDENCE):  # Inverse threshold
                name = known_names[best_match_index]

        top, right, bottom, left = bbox

        # Skip small faces (likely false positives)
        face_size = (right - left) * (bottom - top)
        if face_size < 2500:  # Minimum face size threshold
            continue

        if name == "Unknown":
            # Draw a red box for unknown faces
            if display_frame is not None:
                cv2.rectangle(display_frame, (left, top), (right, bottom), (0, 0, 255), 2)
            continue

        # Extract face image for further checks
        face_img = rgb_frame[top:bottom, left:right]

        # Quick check for masked faces
        if is_masked_face(face_img):
            if display_frame is not None:
                cv2.rectangle(display_frame, (left, top), (right, bottom), (0, 255, 255), 2)
                cv2.putText(display_frame, f"{name} (masked)", (left + 6, bottom - 6),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            continue

        # Liveness check
        if not is_live_face(name, depth_frame, ir_frame, bbox, camera_id):
            if display_frame is not None:
                cv2.rectangle(display_frame, (left, top), (right, bottom), (0, 165, 255), 2)
                cv2.putText(display_frame, f"{name} (spoof)", (left + 6, bottom - 6),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
            continue

        # Update attendance with camera ID
        update_attendance(name, current_time, camera_id)

        # Draw a green box for recognized faces
        if display_frame is not None:
            cv2.rectangle(display_frame, (left, top), (right, bottom), (0, 255, 0), 2)
            cv2.rectangle(display_frame, (left, bottom - 20), (right, bottom), (0, 255, 0), cv2.FILLED)
            cv2.putText(display_frame, name, (left + 6, bottom - 6),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

    # Return the processed frame for display in socket_server.py
    return display_frame

# === LECTURE END MONITOR ===
def check_lecture_end():
    """Checks if lecture time has elapsed or END_LECTURE_KEY was pressed."""
    global lecture_ended

    if lecture_ended:
        return True

    elapsed = (datetime.now() - session_start).total_seconds() / 60
    if elapsed >= LECTURE_DURATION_MINUTES:
        print("[⏳ LECTURE TIME ELAPSED]")
        lecture_ended = True
    # Remove the cv2.waitKey check here as it's already in socket_server.py
    # This prevents duplicate window creation

    return lecture_ended
