
import inspect
import sys
import time
import threading
import sqlite3

# Find all route duplicates
route_map = {}
for name, obj in inspect.getmembers(sys.modules[__name__]):
    if hasattr(obj, '__wrapped__'):
        try:
            route = getattr(obj, '__name__', None)
            line = inspect.getsourcelines(obj)[1]
            if route in route_map:
                route_map[route].append(line)
            else:
                route_map[route] = [line]
        except:
            pass

# Print duplicates
for route, lines in route_map.items():
    if len(lines) > 1:
        print(f"Duplicate function: {route} at lines {lines}")

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, make_response
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.exceptions import HTTPException
import os
from datetime import datetime, timedelta
from io import StringIO  # Add this import for StringIO
import csv
import pdfkit  # Make sure pdfkit is installed: pip install pdfkit
from flask_wtf.csrf import CSRFProtect
from db import (
    init_db, get_all_students, get_all_lecturers, get_all_lectures,
    get_lecture_attendance, create_lecture, log_attendance,
    export_attendance_csv, get_student_by_id, get_lecturer_by_id,
    add_user, get_user_by_username, get_student_attendance,
    get_lecture_by_id, get_students_count, get_lecturers_count,
    get_lectures_count, add_student, add_lecturer, update_student,
    update_lecturer, delete_student, delete_lecturer,
    # Course management
    add_course, update_course, delete_course, get_course_by_id,
    get_course_by_code, get_all_courses,
    # Lecturer-Course assignments
    assign_lecturer_to_course, remove_lecturer_from_course,
    get_lecturer_courses, get_course_lecturers,
    # System settings
    get_system_settings, update_system_setting, update_config_file,
    delete_old_attendance_records, import_faces_to_db,
    # Additional functions
    get_students_enrolled_in_course, get_student_by_user_id,
    get_filtered_student_attendance, record_student_attendance,
    is_student_present, get_users_by_role, get_student_courses,
    create_notification, send_email_notification, update_student_profile,
    delete_notification, log_system_event,
    get_lecturer_by_user_id, update_lecturer_profile,
    get_admin_by_user_id, update_admin_profile, update_lecture_status,
    # New functions
    ensure_lecturer_has_courses, ensure_courses_exist
)
from logger import log_system_event
from notifications import (
    get_user_notifications, mark_notification_read, mark_all_notifications_read,
    delete_notification, get_unread_notification_count, create_notification,
    create_absence_notification, create_late_notification, create_system_notification,
    send_email_notification, send_sms_notification
)

# Add this near the top of the file, after imports
# Create a wrapper function to ensure get_system_settings is always called correctly
def safe_get_settings():
    """Safely get system settings, ensuring the function is called"""
    try:
        # Import the function directly to avoid any potential name conflicts
        from db import get_system_settings as _get_settings
        settings = _get_settings()
        print(f"Settings retrieved successfully: {type(settings)}")
        
        # Remove any settings that might conflict with Flask's built-in template variables
        conflicting_keys = ['csrf_token', 'request', 'session', 'g', 'url_for']
        for key in conflicting_keys:
            if key in settings:
                print(f"WARNING: Removing conflicting key '{key}' from settings")
                settings.pop(key)
        
        # Ensure depth_range_mm is present
        if 'depth_range_mm' not in settings:
            settings['depth_range_mm'] = (500, 3000)
            print("Added missing depth_range_mm setting with default value (500, 3000)")
        
        # Ensure ir_reflectance_threshold is present
        if 'ir_reflectance_threshold' not in settings:
            settings['ir_reflectance_threshold'] = 1000
            print("Added missing ir_reflectance_threshold setting with default value 1000")
        
        # Print each key-value pair to check for callable strings
        for key, value in settings.items():
            print(f"Setting: {key} = {value} (type: {type(value)})")
            if isinstance(value, str) and '(' in value and ')' in value:
                print(f"WARNING: Setting {key} contains parentheses and might be mistakenly called")
        return settings
    except Exception as e:
        app.logger.error(f"Error getting system settings: {str(e)}")
        # Return default settings
        return {
            'late_threshold_minutes': 15,
            'confidence_threshold': 0.85,
            'enable_email_notifications': False,
            'email_server': '',
            'email_port': 587,
            'email_username': '',
            'email_password': '',
            'notify_absences': True,
            'notify_spoofing': True,
            'depth_range_mm': (500, 3000),
            'ir_reflectance_threshold': 1000
        }

# Add this near the top of your file, after the imports
import os
from db import DB_PATH, add_end_time_column  # Import DB_PATH and add_end_time_column from db.py

# Initialize Flask app
app = Flask(__name__, 
            static_folder='static',
            template_folder='templates')
# Use a fixed, strong secret key
app.config['SECRET_KEY'] = '8f42a73054b1749f8f58848be5e6502c'  # Generated with os.urandom(16).hex()
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
app.config['SESSION_COOKIE_SECURE'] = False  # Only set to True if using HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['REMEMBER_COOKIE_DURATION'] = timedelta(days=14)
app.config['REMEMBER_COOKIE_SECURE'] = False  # Only set to True if using HTTPS
app.config['REMEMBER_COOKIE_HTTPONLY'] = True
app.config['REMEMBER_COOKIE_REFRESH_EACH_REQUEST'] = True

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Make sessions permanent by default
@app.before_request
def make_session_permanent():
    session.permanent = True

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Initialize database
init_db()

# Helper functions
def calculate_attendance_percentage(attendance_data):
    if not attendance_data or len(attendance_data) == 0:
        return "0%"
    total_lectures = len(attendance_data)
    attended = sum(1 for record in attendance_data if record)
    percentage = (attended / total_lectures) * 100
    return f"{percentage:.1f}%"

def get_lecturer_lectures(lecturer_id):
    lectures = get_all_lectures(lecturer_id=lecturer_id)
    for lecture in lectures:
        attendance = get_lecture_attendance(lecture['id'])
        lecture['attendance_count'] = len(attendance)
    return lectures

# User class for Flask-Login
class User:
    def __init__(self, id, username, role):
        self.id = id
        self.username = username
        self.role = role
        self.is_authenticated = True
        self.is_active = True
        self.is_anonymous = False
    
    def get_id(self):
        return str(self.id)  # Make sure this returns a string
    
    def __repr__(self):
        return f"<User {self.username}>"

@login_manager.user_loader
def load_user(user_id):
    print(f"Loading user with ID: {user_id}")
    try:
        # Get user by ID from the database
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            c.execute("SELECT id, username, role FROM users WHERE id = ?", (user_id,))
            user_data = c.fetchone()
            
        if user_data:
            print(f"User data found for ID {user_id}: {dict(user_data)}")
            return User(user_data['id'], user_data['username'], user_data['role'])
        print(f"No user data found for ID {user_id}")
    except Exception as e:
        print(f"Error loading user: {str(e)}")
    return None

# Context processor to add notifications to all templates
@app.context_processor
def inject_notifications():
    if current_user.is_authenticated:
        notifications = get_user_notifications(current_user.id, limit=5, include_read=False)
        return {'notifications': notifications}
    return {'notifications': []}

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
        
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('Please enter both username and password', 'danger')
            return render_template('login.html')
        
        user = get_user_by_username(username)
        
        if user and check_password_hash(user['password'], password):
            # Create user object for Flask-Login
            user_obj = User(user['id'], user['username'], user['role'])
            login_user(user_obj)
            
            # Log successful login
            log_system_event(f"User {username} logged in successfully", "INFO")
            
            # Redirect based on role
            if user['role'] == 'admin':
                return redirect(url_for('admin_dashboard'))
            elif user['role'] == 'lecturer':
                return redirect(url_for('lecturer_dashboard'))
            elif user['role'] == 'student':
                return redirect(url_for('student_dashboard'))
            else:
                return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'danger')
            # Log failed login attempt
            log_system_event(f"Failed login attempt for username: {username}", "WARNING")
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    username = current_user.username if hasattr(current_user, 'username') else 'Unknown'
    logout_user()
    flash('You have been logged out', 'info')
    log_system_event(f"User {username} logged out", "INFO")
    return redirect(url_for('login'))

# Role-based dashboard routes
@app.route('/dashboard')
@login_required
def dashboard():
    """Redirect to appropriate dashboard based on user role"""
    print(f"Dashboard accessed by user: {current_user.username}, role: {current_user.role}")
    
    if current_user.role == 'admin':
        return redirect(url_for('admin_dashboard'))
    elif current_user.role == 'lecturer':
        return redirect(url_for('lecturer_dashboard'))
    elif current_user.role == 'student':
        return redirect(url_for('student_dashboard'))
    else:
        flash('Unknown user role', 'warning')
        return render_template('error.html', message="Unknown user role")

@app.route('/admin')
@login_required
def admin_dashboard():
    """Admin dashboard"""
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get counts
    student_count = get_students_count()
    lecturer_count = get_lecturers_count()
    lecture_count = get_lectures_count()
    
    # Get recent lectures
    recent_lectures = get_all_lectures()[:5]  # Get the 5 most recent lectures
    
    # Prepare data for attendance statistics chart
    attendance_stats = {
        'on_time': 0,
        'late': 0,
        'absent': 0
    }
    
    # Prepare data for course distribution chart
    course_distribution = {}
    
    # Calculate statistics from all lectures
    lectures = get_all_lectures()
    for lecture in lectures:
        # Get attendance for this lecture
        attendance = get_lecture_attendance(lecture['id'])
        
        # Count on-time and late students
        on_time = sum(1 for record in attendance if not record.get('is_late'))
        late = sum(1 for record in attendance if record.get('is_late'))
        
        # Add to totals
        attendance_stats['on_time'] += on_time
        attendance_stats['late'] += late
        
        # Add to course distribution
        course_code = lecture.get('course_code', 'Unknown')
        if course_code in course_distribution:
            course_distribution[course_code] += 1
        else:
            course_distribution[course_code] = 1
    
    # Convert course distribution to lists for the chart
    course_labels = list(course_distribution.keys())
    course_values = list(course_distribution.values())
    
    return render_template('admin/dashboard.html',
                          student_count=student_count,
                          lecturer_count=lecturer_count,
                          lecture_count=lecture_count,
                          recent_lectures=recent_lectures,
                          attendance_stats=attendance_stats,
                          course_labels=course_labels,
                          course_values=course_values)

@app.route('/lecturer')
@login_required
def lecturer_dashboard():
    """Lecturer dashboard"""
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get lecturer info
    lecturer = get_lecturer_by_user_id(current_user.id)
    if not lecturer:
        flash('Lecturer profile not found', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get lecturer's courses and lectures
    courses = get_lecturer_courses(lecturer['id'])
    lectures = get_all_lectures(lecturer_id=lecturer['id'])
    
    # Count active lectures
    active_lectures = sum(1 for lecture in lectures if lecture.get('is_active'))
    
    # Prepare data for attendance statistics chart
    attendance_stats = {
        'on_time': 0,
        'late': 0,
        'absent': 0
    }
    
    # Prepare data for course distribution chart
    course_distribution = {}
    
    # Calculate statistics from lectures
    for lecture in lectures:
        # Get attendance for this lecture
        attendance = get_lecture_attendance(lecture['id'])
        
        # Count on-time and late students
        on_time = sum(1 for record in attendance if not record.get('is_late'))
        late = sum(1 for record in attendance if record.get('is_late'))
        
        # Add to totals
        attendance_stats['on_time'] += on_time
        attendance_stats['late'] += late
        
        # Add to course distribution
        course_code = lecture.get('course_code', 'Unknown')
        if course_code in course_distribution:
            course_distribution[course_code] += 1
        else:
            course_distribution[course_code] = 1
    
    # Convert course distribution to lists for the chart
    course_labels = list(course_distribution.keys())
    course_values = list(course_distribution.values())
    
    return render_template('lecturer/dashboard.html', 
                          lecturer=lecturer,
                          courses=courses,
                          lectures=lectures,
                          active_lectures=active_lectures,
                          attendance_stats=attendance_stats,
                          course_labels=course_labels,
                          course_values=course_values)

@app.route('/student')
@login_required
def student_dashboard():
    """Student dashboard"""
    if current_user.role != 'student':
        flash('Access denied. Student privileges required.', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get student details
    student = get_student_by_user_id(current_user.id)
    if not student:
        flash('Student profile not found', 'warning')
        return redirect(url_for('dashboard'))
    
    # Get student's attendance records
    attendance = get_student_attendance(student['id'])
    
    # Calculate attendance rate
    attendance_rate = 0.0
    if attendance:
        attendance_rate = 100.0  # If they have records, they attended 100% of those lectures
    
    # Count late arrivals
    late_count = sum(1 for record in attendance if record.get('is_late'))
    
    # Get total lectures (this is a simplification - you may need to adjust)
    total_lectures = len(attendance)
    
    return render_template('student/dashboard.html', 
                          student=student,
                          attendance=attendance,
                          attendance_history=attendance,  # Add this for compatibility
                          attendance_rate=attendance_rate,
                          late_count=late_count,
                          total_lectures=total_lectures)

@app.route('/simple_dashboard')
@login_required
def simple_dashboard():
    """A simple dashboard that doesn't depend on complex logic"""
    return render_template('simple_dashboard.html', 
                          user=current_user,
                          session_data=session)

# Admin routes
@app.route('/admin/students')
@login_required
def admin_students():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        students = get_all_students()
        return render_template('admin/students.html', students=students)
    except Exception as e:
        app.logger.error(f"Error in admin_students: {str(e)}")
        flash(f"Error loading students: {str(e)}", "danger")
        return redirect(url_for('dashboard'))

@app.route('/admin/add_student', methods=['POST'])
@login_required
def admin_add_student():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        name = request.form.get('name')
        matric_number = request.form.get('matric_number')
        department = request.form.get('department')
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        
        # Validate required fields
        if not all([name, matric_number, department, username, password]):
            flash('All required fields must be filled', 'danger')
            return redirect(url_for('admin_students'))
        
        # Check if username already exists
        existing_user = get_user_by_username(username)
        if existing_user:
            flash(f'Username {username} already exists', 'danger')
            return redirect(url_for('admin_students'))
        
        # Add student
        student_id = add_student(name, matric_number, department, username, password)
        
        # Update additional fields if provided
        if student_id and (email or phone):
            update_student_profile(student_id, email, phone, False, False)
        
        flash(f'Student {name} added successfully', 'success')
        return redirect(url_for('admin_students'))
    except Exception as e:
        app.logger.error(f"Error in admin_add_student: {str(e)}")
        flash(f"Error adding student: {str(e)}", "danger")
        return redirect(url_for('admin_students'))

@app.route('/admin/edit_student/<int:student_id>', methods=['POST'])
@login_required
def admin_edit_student(student_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        name = request.form.get('name')
        matric_number = request.form.get('matric_number')
        department = request.form.get('department')
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Validate required fields
        if not all([name, matric_number, department]):
            flash('Name, matric number, and department are required', 'danger')
            return redirect(url_for('admin_students'))
        
        # Get current student data
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            c.execute("SELECT user_id FROM students WHERE id = ?", (student_id,))
            student = c.fetchone()
            
            if not student:
                flash('Student not found', 'danger')
                return redirect(url_for('admin_students'))
            
            # Update student basic info
            c.execute("""
                UPDATE students 
                SET name = ?, matric_number = ?, department = ?
                WHERE id = ?
            """, (name, matric_number, department, student_id))
            
            # If username and password are provided and user doesn't exist yet, create one
            if username and password and not student['user_id']:
                # Check if username already exists
                c.execute("SELECT id FROM users WHERE username = ?", (username,))
                existing_user = c.fetchone()
                
                if existing_user:
                    flash(f'Username {username} already exists', 'danger')
                else:
                    # Create user account
                    hashed_password = generate_password_hash(password)
                    c.execute("""
                        INSERT INTO users (username, password, role)
                        VALUES (?, ?, ?)
                    """, (username, hashed_password, 'student'))
                    
                    user_id = c.lastrowid
                    
                    # Link user to student
                    c.execute("UPDATE students SET user_id = ? WHERE id = ?", (user_id, student_id))
                    
                    flash('User account created and linked to student', 'success')
            
            flash('Student updated successfully', 'success')
            log_system_event(f"Student {name} (ID: {student_id}) updated by admin {current_user.username}")
        
        return redirect(url_for('admin_students'))
    except Exception as e:
        app.logger.error(f"Error in admin_edit_student: {str(e)}")
        flash(f"Error updating student: {str(e)}", "danger")
        return redirect(url_for('admin_students'))

@app.route('/admin/delete_student/<int:student_id>')
@login_required
def admin_delete_student(student_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    delete_student(student_id)
    flash('Student deleted successfully', 'success')
    return redirect(url_for('admin_students'))

@app.route('/admin/lecturers')
@login_required
def admin_lecturers():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        lecturers = get_all_lecturers()
        return render_template('admin/lecturers.html', lecturers=lecturers)
    except Exception as e:
        app.logger.error(f"Error in admin_lecturers: {str(e)}")
        flash(f"Error loading lecturers: {str(e)}", "danger")
        return redirect(url_for('dashboard'))

@app.route('/admin/add_lecturer', methods=['POST'])
@login_required
def admin_add_lecturer():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        name = request.form.get('name')
        staff_id = request.form.get('staff_id', '')
        department = request.form.get('department', '')
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        
        # Validate required fields
        if not all([name, username, password]):
            flash('Name, username and password are required', 'danger')
            return redirect(url_for('admin_lecturers'))
        
        # Check if username already exists
        existing_user = get_user_by_username(username)
        if existing_user:
            flash(f'Username {username} already exists', 'danger')
            return redirect(url_for('admin_lecturers'))
        
        # Add lecturer
        lecturer_id = add_lecturer(name, username, password, staff_id, department)
        
        # Update additional fields if provided
        if lecturer_id and (email or phone):
            update_lecturer_profile(lecturer_id, email, phone, False, False)
        
        flash(f'Lecturer {name} added successfully', 'success')
        return redirect(url_for('admin_lecturers'))
    except Exception as e:
        app.logger.error(f"Error in admin_add_lecturer: {str(e)}")
        flash(f"Error adding lecturer: {str(e)}", "danger")
        return redirect(url_for('admin_lecturers'))

@app.route('/admin/edit_lecturer/<int:lecturer_id>', methods=['POST'])
@login_required
def admin_edit_lecturer(lecturer_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        name = request.form.get('name')
        staff_id = request.form.get('staff_id')
        department = request.form.get('department')
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        
        # Validate required fields
        if not all([name, staff_id, department]):
            flash('Name, staff ID, and department are required', 'danger')
            return redirect(url_for('admin_lecturers'))
        
        # Get current lecturer data
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            c.execute("""
                SELECT l.*, u.id as user_id, u.username 
                FROM lecturers l
                LEFT JOIN users u ON l.user_id = u.id
                WHERE l.id = ?
            """, (lecturer_id,))
            lecturer = c.fetchone()
            
            if not lecturer:
                flash('Lecturer not found', 'danger')
                return redirect(url_for('admin_lecturers'))
            
            # Update lecturer basic info
            c.execute("""
                UPDATE lecturers 
                SET name = ?, staff_id = ?, department = ?, email = ?, phone = ?
                WHERE id = ?
            """, (name, staff_id, department, email, phone, lecturer_id))
            
            # Handle user account updates
            if lecturer['user_id']:
                # User exists, check if password needs to be updated
                if password:
                    hashed_password = generate_password_hash(password)
                    c.execute("UPDATE users SET password = ? WHERE id = ?", 
                             (hashed_password, lecturer['user_id']))
                    flash('Password updated successfully', 'success')
            elif username and password:
                # Create new user account
                hashed_password = generate_password_hash(password)
                c.execute("""
                    INSERT INTO users (username, password, role)
                    VALUES (?, ?, ?)
                """, (username, hashed_password, 'lecturer'))
                
                user_id = c.lastrowid
                
                # Link user to lecturer
                c.execute("UPDATE lecturers SET user_id = ? WHERE id = ?", 
                         (user_id, lecturer_id))
                
                flash('User account created and linked to lecturer', 'success')
            
            flash('Lecturer updated successfully', 'success')
            log_system_event(f"Lecturer {name} (ID: {lecturer_id}) updated by admin {current_user.username}")
        
        return redirect(url_for('admin_lecturers'))
    except Exception as e:
        app.logger.error(f"Error in admin_edit_lecturer: {str(e)}")
        flash(f"Error updating lecturer: {str(e)}", "danger")
        return redirect(url_for('admin_lecturers'))

@app.route('/admin/delete_lecturer/<int:lecturer_id>')
@login_required
def admin_delete_lecturer(lecturer_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    delete_lecturer(lecturer_id)
    flash('Lecturer deleted successfully', 'success')
    return redirect(url_for('admin_lecturers'))

@app.route('/admin/lectures')
@login_required
def admin_lectures():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        lectures = get_all_lectures()
        courses = get_all_courses()
        lecturers = get_all_lecturers()
        
        return render_template('admin/lectures.html', 
                              lectures=lectures,
                              courses=courses,
                              lecturers=lecturers)
    except Exception as e:
        app.logger.error(f"Error in admin_lectures: {str(e)}")
        flash(f"Error loading lectures: {str(e)}", "danger")
        return redirect(url_for('dashboard'))

@app.route('/admin/add_lecture', methods=['POST'])
@login_required
def admin_add_lecture():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    try:
        course_id = request.form.get('course_id')
        lecturer_id = request.form.get('lecturer_id')
        date = request.form.get('date')
        start_time = request.form.get('start_time')
        duration = request.form.get('duration')
        room = request.form.get('room')
        
        # Validate required fields
        if not all([course_id, lecturer_id, date, start_time, duration]):
            flash('All required fields must be filled', 'danger')
            return redirect(url_for('admin_lectures'))
        
        # Format datetime
        start_datetime = f"{date} {start_time}"
        
        # Create lecture
        lecture_id = create_lecture(course_id, lecturer_id, start_datetime, duration, room)
        
        if lecture_id:
            flash('Lecture created successfully', 'success')
        else:
            flash('Failed to create lecture', 'danger')
            
        return redirect(url_for('admin_lectures'))
    except Exception as e:
        app.logger.error(f"Error in admin_add_lecture: {str(e)}")
        flash(f"Error adding lecture: {str(e)}", "danger")
        return redirect(url_for('admin_lectures'))

@app.route('/admin/view_lecture/<int:lecture_id>')
@login_required
def admin_view_lecture(lecture_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    lecture = get_lecture_by_id(lecture_id)
    attendance = get_lecture_attendance(lecture_id)
    
    # Calculate statistics
    total_students = len(attendance)
    on_time_count = sum(1 for record in attendance if not record[3])
    late_count = sum(1 for record in attendance if record[3])
    
    # Calculate attendance percentage based on expected class size
    expected_students = get_students_count()  # This is a simplification
    attendance_percentage = (total_students / expected_students * 100) if expected_students > 0 else 0
    absent_count = expected_students - total_students
    
    return render_template('admin/view_lecture_attendance.html',
                          lecture=lecture,
                          attendance=attendance,
                          attendance_percentage=f"{attendance_percentage:.1f}%",
                          on_time_count=on_time_count,
                          late_count=late_count,
                          absent_count=absent_count)

@app.route('/admin/export_lecture/<int:lecture_id>')
@login_required
def admin_export_lecture(lecture_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    filename = f"attendance_{lecture_id}_{datetime.now().strftime('%Y%m%d')}.csv"
    export_path = os.path.join('exports', filename)
    
    os.makedirs('exports', exist_ok=True)
    export_attendance_csv(lecture_id, export_path)
    
    # In a real app, you'd return the file for download
    flash(f'Attendance exported to {export_path}', 'success')
    return redirect(url_for('admin_view_lecture', lecture_id=lecture_id))

# Lecturer routes
@app.route('/start_lecture', methods=['GET', 'POST'])
@login_required
def start_lecture():
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get lecturer info
    lecturer = get_lecturer_by_user_id(current_user.id)
    if not lecturer:
        flash('Lecturer profile not found', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get lecturer's courses
    lecturer_courses = get_lecturer_courses(lecturer['id'])
    
    # Handle form submission
    if request.method == 'POST':
        course_id = request.form.get('course')
        duration = request.form.get('duration')
        room = request.form.get('room', '')  # Optional room field
        
        if not course_id or not duration:
            flash('Please fill all required fields', 'danger')
            return render_template('lecturer/start_lecture.html', 
                                  lecturer_courses=lecturer_courses,
                                  selected_course=course_id)
        
        try:
            duration = int(duration)
            if duration < 15 or duration > 240:
                raise ValueError("Duration must be between 15 and 240 minutes")
        except ValueError:
            flash('Duration must be a number between 15 and 240', 'danger')
            return render_template('lecturer/start_lecture.html', 
                                  lecturer_courses=lecturer_courses,
                                  selected_course=course_id)
        
        # Get current date and time for start_time
        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Create the lecture
        lecture_id = create_lecture(course_id, lecturer['id'], start_time, duration, room)
        
        if lecture_id:
            flash('Lecture started successfully', 'success')
            return redirect(url_for('view_lecture', lecture_id=lecture_id))
        else:
            flash('Failed to start lecture', 'danger')
    
    # Pre-select course if provided in query string
    selected_course = request.args.get('course', '')
    
    return render_template('lecturer/start_lecture.html', 
                          lecturer_courses=lecturer_courses,
                          selected_course=selected_course)

@app.route('/lecturer/lecture/<int:lecture_id>')
@login_required
def view_lecture(lecture_id):
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    lecture = get_lecture_by_id(lecture_id)
    if not lecture:
        flash('Lecture not found', 'danger')
        return redirect(url_for('dashboard'))
    
    # Check if the lecture belongs to the current lecturer
    lecturer = get_lecturer_by_user_id(current_user.id)
    if not lecturer or lecture['lecturer_id'] != lecturer['id']:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get attendance records
    attendance = get_lecture_attendance(lecture_id)
    
    # Calculate statistics
    total_students = len(attendance)
    on_time_count = sum(1 for record in attendance if not record.get('is_late', False))
    late_count = sum(1 for record in attendance if record.get('is_late', False))
    
    # Get all departments for filtering
    departments = get_all_departments()

    # Calculate attendance percentage based on expected class size
    course = get_course_by_id(lecture['course_id'])
    enrolled_students = get_students_enrolled_in_course(lecture['course_id']) if course else []
    expected_students = len(enrolled_students)
    attendance_percentage = (total_students / expected_students * 100) if expected_students > 0 else 0
    absent_count = expected_students - total_students

    # Get system settings
    settings = safe_get_settings()
    
    # Calculate elapsed time
    elapsed_minutes = calculate_lecture_elapsed_time(lecture)

    return render_template('lecturer/view_lecture.html',
                          lecture_id=lecture_id,
                          lecture=lecture,
                          attendance=attendance,
                          attendance_percentage=f"{attendance_percentage:.1f}%",
                          late_count=late_count,
                          on_time_count=on_time_count,
                          absent_count=absent_count,
                          present_count=total_students,
                          departments=departments,
                          settings=settings,
                          elapsed_minutes=elapsed_minutes)

@app.route('/lecturer/export/<int:lecture_id>')
@login_required
def export_lecture(lecture_id):
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    filename = f"attendance_{lecture_id}_{datetime.now().strftime('%Y%m%d')}.csv"
    export_path = os.path.join('exports', filename)

    os.makedirs('exports', exist_ok=True)
    export_attendance_csv(lecture_id, export_path)

    # In a real app, you'd return the file for download
    flash(f'Attendance exported to {export_path}', 'success')
    return redirect(url_for('view_lecture', lecture_id=lecture_id))  # Added missing closing parenthesis

@app.route('/api/start_camera_server', methods=['POST'])
@login_required
def start_camera_server():
    """API endpoint to start the camera server for a lecture"""
    if current_user.role != 'lecturer':
        return jsonify({'success': False, 'error': 'Access denied'}), 403
    
    try:
        data = request.json
        lecture_id = data.get('lecture_id')
        
        if not lecture_id:
            return jsonify({'success': False, 'error': 'Missing lecture ID'}), 400
        
        # Get lecture details
        lecture = get_lecture_by_id(lecture_id)
        if not lecture:
            return jsonify({'success': False, 'error': 'Lecture not found'}), 404
        
        # Check if the lecture belongs to the current lecturer
        lecturer = get_lecturer_by_user_id(current_user.id)
        if not lecturer or lecture['lecturer_id'] != lecturer['id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403
        
        # Check if lecture is active
        if not lecture.get('is_active'):
            return jsonify({'success': False, 'error': 'Lecture is not active'}), 400
        
        # Check if socket server is already running
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        try:
            # Try to connect to the socket server
            result = sock.connect_ex(('localhost', 9090))
            if result == 0:
                # Port is open, server is likely running
                app.logger.info("Socket server is already running on port 9090")
                return jsonify({'success': True, 'message': 'Camera server is already running'})
        except:
            pass
        finally:
            sock.close()
        
        # Start the camera server process
        try:
            # Import here to avoid circular imports
            import subprocess
            import sys
            import os
            
            # Log the attempt
            app.logger.info(f"Starting camera server for lecture ID {lecture_id}")
            
            # Get the path to the Python executable
            python_exe = sys.executable
            
            # Get the path to the socket_server.py file
            socket_server_path = os.path.join(os.path.dirname(__file__), 'socket_server.py')
            
            # Start the process
            process = subprocess.Popen(
                [python_exe, socket_server_path, str(lecture_id)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_CONSOLE  # This shows a new console window on Windows
            )
            
            # Check if process started successfully
            if process.poll() is None:
                app.logger.info(f"Camera server process started with PID {process.pid}")
                return jsonify({'success': True, 'message': 'Camera server started successfully'})
            else:
                stdout, stderr = process.communicate(timeout=2)
                error_msg = f"Process exited immediately: {stderr}"
                app.logger.error(error_msg)
                return jsonify({'success': False, 'error': error_msg}), 500
                
        except Exception as e:
            error_msg = f"Failed to start camera server: {str(e)}"
            app.logger.error(error_msg)
            return jsonify({'success': False, 'error': error_msg}), 500
            
    except Exception as e:
        app.logger.error(f"Error in start_camera_server: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Student routes
@app.route('/student/attendance')
@login_required
def student_attendance():
    if current_user.role != 'student':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    student = get_student_by_user_id(current_user.id)
    if not student:
        flash('Student profile not found', 'danger')
        return redirect(url_for('dashboard'))

    # Get filter parameters
    course_filter = request.args.get('course', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Get attendance data with filters
    attendance_data = get_filtered_student_attendance(student['id'], course_filter, date_from, date_to)

    # Get courses for filter dropdown
    courses = get_student_courses(student['id'])

    # Calculate attendance percentage
    attendance_percentage = calculate_attendance_percentage(attendance_data)

    return render_template('student/attendance.html',
                          attendance=attendance_data,
                          courses=courses,
                          attendance_percentage=attendance_percentage,
                          course_filter=course_filter,
                          date_from=date_from,
                          date_to=date_to)

@app.route('/student/export_attendance/<format>')
@login_required
def export_student_attendance(format):
    if current_user.role != 'student':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    student_id = request.args.get('student_id')
    course = request.args.get('course', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')

    # Verify student_id belongs to current user
    student = get_student_by_user_id(current_user.id)
    if not student or str(student['id']) != student_id:
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    # Get filtered attendance data
    attendance_data = get_filtered_student_attendance(
        student_id, course, date_from, date_to)

    if format == 'csv':
        # Generate CSV
        filename = f"attendance_{student['matric_number']}_{datetime.now().strftime('%Y%m%d')}.csv"

        # Create CSV in memory
        output = StringIO()
        writer = csv.writer(output)

        # Write headers
        writer.writerow(['Student Name', 'Matric Number', 'Course Code', 'Course Name',
                         'Date', 'Time', 'Duration (mins)', 'Status'])

        # Write data
        for record in attendance_data:
            writer.writerow([
                student['name'],
                student['matric_number'],
                record['course_code'],
                record['course_name'],
                record['lecture_date'].split(' ')[0],  # Just the date part
                record['attendance_time'].split(' ')[1],  # Just the time part
                record['duration_minutes'],
                'Late' if record['is_late'] else 'On Time'
            ])

        # Create response
        response = make_response(output.getvalue())
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        response.headers["Content-Type"] = "text/csv"
        return response

    elif format == 'pdf':
        # Generate PDF
        filename = f"attendance_{student['matric_number']}_{datetime.now().strftime('%Y%m%d')}.pdf"

        # Create HTML for PDF
        html = render_template('exports/student_attendance_pdf.html',
                              student=student,
                              attendance_data=attendance_data,
                              course_filter=course,
                              date_from=date_from,
                              date_to=date_to,
                              generated_date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Convert HTML to PDF
        pdf = pdfkit.from_string(html, False)

        # Create response
        response = make_response(pdf)
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        response.headers["Content-Type"] = "application/pdf"
        return response

    else:
        flash('Invalid export format', 'danger')
        return redirect(url_for('student_attendance'))

# API endpoints for the socket server to use
@app.route('/api/log_attendance', methods=['POST'])
def api_log_attendance():
    # This would be secured with API keys in production
    data = request.json
    lecture_id = data.get('lecture_id')
    matric_number = data.get('matric_number')

    if not lecture_id or not matric_number:
        return jsonify({'error': 'Missing required fields'}), 400

    log_attendance(lecture_id, matric_number)
    return jsonify({'success': True})

# Course management routes
@app.route('/admin/courses')
@login_required
def admin_courses():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        courses = get_all_courses()
        return render_template('admin/courses.html', courses=courses)
    except Exception as e:
        app.logger.error(f"Error in admin_courses: {str(e)}")
        flash(f"Error loading courses: {str(e)}", "danger")
        return redirect(url_for('dashboard'))

@app.route('/admin/add_course', methods=['POST'])
@login_required
def admin_add_course():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        code = request.form.get('code')
        title = request.form.get('title')
        department = request.form.get('department')

        # Validate required fields
        if not all([code, title, department]):
            flash('All fields are required', 'danger')
            return redirect(url_for('admin_courses'))

        # Check if course code already exists
        existing_course = get_course_by_code(code)
        if existing_course:
            flash(f'Course code {code} already exists', 'danger')
            return redirect(url_for('admin_courses'))

        # Add course
        add_course(code, title, department)

        flash(f'Course {code} added successfully', 'success')
        return redirect(url_for('admin_courses'))
    except Exception as e:
        app.logger.error(f"Error in admin_add_course: {str(e)}")
        flash(f"Error adding course: {str(e)}", "danger")
        return redirect(url_for('admin_courses'))

@app.route('/admin/edit_course/<int:course_id>', methods=['POST'])
@login_required
def admin_edit_course(course_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    code = request.form.get('code')
    title = request.form.get('title')
    department = request.form.get('department')

    update_course(course_id, code, title, department)
    flash(f'Course updated successfully', 'success')
    return redirect(url_for('admin_courses'))

@app.route('/admin/delete_course/<int:course_id>')
@login_required
def admin_delete_course(course_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    delete_course(course_id)
    flash('Course deleted successfully', 'success')
    return redirect(url_for('admin_courses'))

@app.route('/admin/course/<int:course_id>/lecturers')
@login_required
def admin_course_lecturers(course_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        course = get_course_by_id(course_id)
        if not course:
            flash('Course not found', 'danger')
            return redirect(url_for('admin_courses'))

        assigned_lecturers = get_course_lecturers(course_id)
        all_lecturers = get_all_lecturers()

        # Filter out already assigned lecturers
        assigned_ids = [l['id'] for l in assigned_lecturers]
        available_lecturers = [l for l in all_lecturers if l['id'] not in assigned_ids]

        return render_template('admin/course_lecturers.html',
                              course=course,
                              assigned_lecturers=assigned_lecturers,
                              available_lecturers=available_lecturers)
    except Exception as e:
        app.logger.error(f"Error in admin_course_lecturers: {str(e)}")
        flash(f"Error loading course lecturers: {str(e)}", "danger")
        return redirect(url_for('admin_courses'))

@app.route('/admin/course/<int:course_id>/assign_lecturer', methods=['POST'])
@login_required
def admin_assign_lecturer(course_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        lecturer_id = request.form.get('lecturer_id')
        if not lecturer_id:
            flash('No lecturer selected', 'danger')
            return redirect(url_for('admin_course_lecturers', course_id=course_id))

        assign_lecturer_to_course(lecturer_id, course_id)
        flash('Lecturer assigned to course successfully', 'success')
        return redirect(url_for('admin_course_lecturers', course_id=course_id))
    except Exception as e:
        app.logger.error(f"Error in admin_assign_lecturer: {str(e)}")
        flash(f"Error assigning lecturer: {str(e)}", "danger")
        return redirect(url_for('admin_course_lecturers', course_id=course_id))

@app.route('/admin/course/<int:course_id>/remove_lecturer/<int:lecturer_id>', methods=['POST'])
@login_required
def admin_remove_lecturer(course_id, lecturer_id):
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        remove_lecturer_from_course(lecturer_id, course_id)
        flash('Lecturer removed from course successfully', 'success')
        return redirect(url_for('admin_course_lecturers', course_id=course_id))
    except Exception as e:
        app.logger.error(f"Error in admin_remove_lecturer: {str(e)}")
        flash(f"Error removing lecturer: {str(e)}", "danger")
        return redirect(url_for('admin_course_lecturers', course_id=course_id))

@app.route('/lecturer/my_courses')
@login_required
def lecturer_courses():
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    # Get lecturer info by user_id
    lecturer = get_lecturer_by_user_id(current_user.id)
    if not lecturer:
        flash('Lecturer profile not found', 'danger')
        return redirect(url_for('dashboard'))
    
    # Get lecturer's courses
    courses = get_lecturer_courses(lecturer['id'])
    
    # If no courses, try to assign some directly
    if not courses:
        try:
            # Get all courses
            all_courses = get_all_courses()
            
            # Assign up to 3 courses to the lecturer
            courses_added = 0
            for course in all_courses[:3]:  # Limit to first 3 courses
                try:
                    assign_lecturer_to_course(lecturer['id'], course['id'])
                    courses_added += 1
                except Exception as e:
                    app.logger.error(f"Error assigning course {course['id']} to lecturer {lecturer['id']}: {str(e)}")
            
            if courses_added > 0:
                flash(f'{courses_added} sample courses have been assigned to you for testing', 'info')
                # Get updated courses list
                courses = get_lecturer_courses(lecturer['id'])
        except Exception as e:
            app.logger.error(f"Error assigning courses to lecturer: {str(e)}")
    
    # Debug print
    print(f"Lecturer ID: {lecturer['id']}, Found courses: {len(courses)}")
    for course in courses:
        print(f"Course: {course['code']} - {course['title']}")
    
    return render_template('lecturer/my_courses.html', courses=courses)

@app.route('/admin/system_settings')
@login_required
def admin_system_settings():
    if current_user.role != 'admin':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    try:
        # Use the safe function to get settings
        settings = safe_get_settings()

        # Remove any settings that might conflict with Flask's built-in template variables
        conflicting_keys = ['csrf_token', 'request', 'session', 'g', 'url_for']
        for key in conflicting_keys:
            if key in settings:
                print(f"WARNING: Removing conflicting key '{key}' from settings")
                settings.pop(key)

        # Render the template with settings
        return render_template('admin/system_settings.html', settings=settings)
    except Exception as e:
        import traceback
        traceback_str = traceback.format_exc()
        print(f"FULL TRACEBACK:\n{traceback_str}")
        app.logger.error(f"Error in admin_system_settings: {str(e)}\n{traceback_str}")
        flash(f"Error loading system settings: {str(e)}", "danger")
        return redirect(url_for('dashboard'))

@app.route('/admin/save_settings', methods=['POST'])
@login_required
def admin_save_settings():
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'Access denied'})

    try:
        data = request.json
        settings_type = data.get('type')
        settings = data.get('settings', {})

        # Update settings in database
        for key, value in settings.items():
            update_system_setting(key, value)

        # If updating attendance settings, also update config file
        if settings_type == 'attendance':
            update_config_file(settings)

        log_system_event(f"System settings updated by {current_user.username}")

        return jsonify({'success': True})
    except Exception as e:
        log_system_event(f"Error saving settings: {str(e)}", "ERROR")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/backup_database', methods=['POST'])
@login_required
def admin_backup_database():
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'Access denied'})

    try:
        # Create a copy of the database file
        backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(os.path.dirname(DB_PATH), backup_filename)

        with open(DB_PATH, 'rb') as src, open(backup_path, 'wb') as dst:
            dst.write(src.read())

        log_system_event(f"Database backup created: {backup_filename}")

        # Send the file to the client
        return send_file(backup_path, as_attachment=True, download_name=backup_filename)
    except Exception as e:
        log_system_event(f"Error creating database backup: {str(e)}", "ERROR")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/import_faces', methods=['POST'])
@login_required
def admin_import_faces():
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    try:
        # Import faces from the images directory
        result = import_faces_to_db()

        # Log the import
        log_system_event(f"Face encodings imported by admin {current_user.username}: {result['students']} students, {result['lecturers']} lecturers")

        return jsonify({
            'success': True,
            'message': f"Successfully imported {result['students']} student and {result['lecturers']} lecturer face encodings"
        })
    except Exception as e:
        app.logger.error(f"Error in admin_import_faces: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/admin/clear_logs', methods=['POST'])
@login_required
def admin_clear_logs():
    if current_user.role != 'admin':
        return jsonify({'success': False, 'error': 'Access denied'})

    try:
        data = request.json
        date = data.get('date')

        if not date:
            return jsonify({'success': False, 'error': 'No date provided'})

        # Delete attendance records older than the given date
        deleted_count = delete_old_attendance_records(date)

        log_system_event(f"Deleted {deleted_count} attendance records older than {date}")
        return jsonify({'success': True, 'count': deleted_count})
    except Exception as e:
        log_system_event(f"Error clearing attendance logs: {str(e)}", "ERROR")
        return jsonify({'success': False, 'error': str(e)})

# Notification routes
@app.route('/all_notifications')
@login_required
def view_all_notifications():
    notifications = get_user_notifications(current_user.id, include_read=True)
    return render_template('notifications.html', notifications=notifications)

@app.route('/notifications/mark_read/<int:notification_id>', methods=['POST'])
@login_required
def mark_notification_as_read_route(notification_id):
    success = mark_notification_read(notification_id)
    return jsonify({'success': success})

@app.route('/notifications/mark_all_read', methods=['POST'])
@login_required
def mark_all_notifications_as_read_route():
    count = mark_all_notifications_read(current_user.id)
    return jsonify({'success': True, 'count': count})

@app.route('/notifications/delete/<int:notification_id>', methods=['POST'])
@login_required
def delete_user_notification(notification_id):
    success = delete_notification(notification_id)
    return jsonify({'success': success})

# API endpoint to get unread notification count
@app.route('/api/notifications/total')
@login_required
def notification_total_count():
    count = get_unread_notification_count(current_user.id)
    return jsonify({'count': count})

# API endpoint to report spoofing attempt
@app.route('/api/report_spoofing', methods=['POST'])
def report_spoofing():
    try:
        data = request.json

        if not data or 'student_id' not in data or 'lecture_id' not in data:
            return jsonify({'success': False, 'error': 'Invalid data'}), 400

        student_id = data.get('student_id')
        lecture_id = data.get('lecture_id')
        confidence = data.get('confidence', 0)

        # Log the spoofing attempt
        log_system_event(f"Spoofing attempt detected for student ID {student_id} at lecture ID {lecture_id} with confidence {confidence}", "WARNING")

        # Get system settings - make sure to call the function
        settings = safe_get_settings()
        notify_spoofing = settings.get('notify_spoofing', 'true') == 'true'

        if notify_spoofing:
            # Get all admin users
            admin_users = get_users_by_role('admin')

            # Get student and lecture details
            student = get_student_by_id(student_id)
            lecture = get_lecture_by_id(lecture_id)

            if student and lecture:
                # Create notification message
                message = f"Spoofing attempt detected for student {student['name']} ({student['matric_number']}) at {lecture['course_code']} lecture"

                # Notify all admins
                for admin in admin_users:
                    create_notification(admin['id'], message, 'spoofing', lecture_id)

                    # Send email if configured
                    if admin.get('email'):
                        subject = "ALERT: Spoofing Attempt Detected"
                        email_message = f"""
                        SECURITY ALERT: Potential spoofing attempt detected
                        
                        Details:
                        - Student: {student['name']} ({student['matric_number']})
                        - Course: {lecture['course_code']}
                        - Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                        - Confidence: {confidence}
                        
                        Please review the security logs and take appropriate action.
                        
                        This is an automated message from the Biometric Attendance System.
                        """
                        send_email_notification(admin['email'], subject, email_message)

        return jsonify({'success': True})
    except Exception as e:
        import traceback
        traceback_str = traceback.format_exc()
        print(f"FULL TRACEBACK in report_spoofing:\n{traceback_str}")
        app.logger.error(f"Error in report_spoofing: {str(e)}\n{traceback_str}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('404.html'), 404

@app.errorhandler(403)
def forbidden(e):
    return render_template('error.html',
                          error_title='Access Denied',
                          error_message='You do not have permission to access this resource.'), 403

@app.errorhandler(500)
def internal_server_error(e):
    return render_template('error.html',
                          error_title='Internal Server Error',
                          error_message='Something went wrong on our end. Please try again later.'), 500

@app.errorhandler(405)
def method_not_allowed(e):
    return render_template('error.html',
                          error_title='Method Not Allowed',
                          error_message='The method is not allowed for the requested URL.'), 405

# Custom error handler for all HTTP exceptions
@app.errorhandler(Exception)
def handle_exception(e):
    # Pass through HTTP errors
    if isinstance(e, HTTPException):
        return e

    # Log the error with detailed information
    app.logger.error(f"Unhandled exception: {str(e)}")
    import traceback
    app.logger.error(traceback.format_exc())

    # Now handle non-HTTP exceptions
    return render_template('error.html',
                          error_title='Unexpected Error',
                          error_message=f'An unexpected error occurred: {str(e)}'), 500

# Function to process absences after a lecture ends
def process_lecture_absences(lecture_id):
    """Process absences for a lecture and send notifications"""
    # Get lecture details
    lecture = get_lecture_by_id(lecture_id)
    if not lecture:
        return False

    # Get all students enrolled in the course
    enrolled_students = get_students_enrolled_in_course(lecture['course_id'])

    # Get students who attended
    attendance = get_lecture_attendance(lecture_id)
    attended_student_ids = [record['student_id'] for record in attendance]

    # Find absent students
    absent_student_ids = [s['id'] for s in enrolled_students if s['id'] not in attended_student_ids]

    # Get system settings - make sure to call the function
    settings = get_system_settings()
    notify_absences = settings.get('notify_absences', 'true') == 'true'

    if notify_absences:
        # Create notifications for absent students
        for student_id in absent_student_ids:
            student = get_student_by_id(student_id)
            if student:
                # Create in-app notification
                create_absence_notification(student_id, lecture_id)

                # Send SMS if phone number is available and SMS notifications are enabled
                if student.get('phone') and student.get('sms_notifications') == 'true':
                    message = f"You were absent from {lecture['course_code']} lecture on {lecture['start_time'].split(' ')[0]}"
                    send_sms_notification(student['phone'], message)

                # Send email if email is available and email notifications are enabled
                if student.get('email') and student.get('email_notifications') == 'true':
                    subject = f"Absence Notification - {lecture['course_code']}"
                    message = f"""
                    Dear {student['name']},
                    
                    Our records indicate that you were absent from the following lecture:
                    
                    Course: {lecture['course_code']} - {lecture['course_name']}
                    Date: {lecture['start_time'].split(' ')[0]}
                    Time: {lecture['start_time'].split(' ')[1]}
                    
                    If you believe this is an error, please contact your lecturer.
                    
                    Regards,
                    Biometric Attendance System
                    """
                    send_email_notification(student['email'], subject, message)

    return True

# Route to end a lecture and process absences
@app.route('/lecturer/end_lecture/<int:lecture_id>', methods=['POST'])
@login_required
def end_lecture(lecture_id):
    if current_user.role != 'lecturer':
        flash('Access denied', 'danger')
        return redirect(url_for('dashboard'))

    # Verify CSRF token
    if request.method == 'POST':
        # Get lecture details
        lecture = get_lecture_by_id(lecture_id)
        if not lecture:
            flash('Lecture not found', 'danger')
            return redirect(url_for('dashboard'))
        
        # Check if the lecture belongs to the current lecturer
        lecturer = get_lecturer_by_user_id(current_user.id)
        if not lecturer or lecture['lecturer_id'] != lecturer['id']:
            flash('Access denied', 'danger')
            return redirect(url_for('dashboard'))
        
        # Update lecture status
        success = update_lecture_status(lecture_id, is_active=False)

        if success:
            # Process absences and send notifications
            process_lecture_absences(lecture_id)
            flash('Lecture ended successfully', 'success')
        else:
            flash('Failed to end lecture', 'danger')

    return redirect(url_for('dashboard'))

@app.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    if current_user.role == 'student':
        student = get_student_by_user_id(current_user.id)

        # Make sure we have the username
        if student:
            # Get the username from the users table
            with sqlite3.connect(DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                c = conn.cursor()
                c.execute("SELECT username FROM users WHERE id = ?", (current_user.id,))
                user = c.fetchone()
                if user:
                    student['username'] = user['username']

        if request.method == 'POST':
            # Update student profile
            email = request.form.get('email')
            phone = request.form.get('phone')

            # Update notification preferences
            email_notifications = 'true' if 'email_notifications' in request.form else 'false'
            sms_notifications = 'true' if 'sms_notifications' in request.form else 'false'

            # Update student record
            update_student_profile(student['id'], email, phone, email_notifications, sms_notifications)

            flash('Profile updated successfully', 'success')
            return redirect(url_for('profile'))

        return render_template('student/profile.html', student=student)

    elif current_user.role == 'lecturer':
        lecturer = get_lecturer_by_user_id(current_user.id)

        if request.method == 'POST':
            # Update lecturer profile
            email = request.form.get('email')
            phone = request.form.get('phone')

            # Update notification preferences
            email_notifications = 'email_notifications' in request.form
            sms_notifications = 'sms_notifications' in request.form

            # Update lecturer record
            update_lecturer_profile(lecturer['id'], email, phone, email_notifications, sms_notifications)

            flash('Profile updated successfully', 'success')
            return redirect(url_for('profile'))

        return render_template('lecturer/profile.html', lecturer=lecturer)

    elif current_user.role == 'admin':
        admin = get_admin_by_user_id(current_user.id)

        if request.method == 'POST':
            # Update admin profile
            email = request.form.get('email')
            phone = request.form.get('phone')

            # Update notification preferences
            email_notifications = 'email_notifications' in request.form
            sms_notifications = 'sms_notifications' in request.form

            # Update admin record
            update_admin_profile(admin['id'], email, phone, email_notifications, sms_notifications)

            flash('Profile updated successfully', 'success')
            return redirect(url_for('profile'))

        return render_template('admin/profile.html', admin=admin)

    return redirect(url_for('dashboard'))

# Route to view notifications
@app.route('/notifications')
@login_required
def view_notifications():
    notifications = get_user_notifications(current_user.id)
    return render_template('notifications.html', notifications=notifications)

# API route to mark a notification as read
@app.route('/notifications/mark_read/<int:notification_id>', methods=['POST'])
@login_required
def mark_read(notification_id):
    success = mark_notification_read(notification_id)
    return jsonify({'success': success})

# API route to mark all notifications as read
@app.route('/notifications/mark_all_read', methods=['POST'])
@login_required
def mark_all_read():
    count = mark_all_notifications_read(current_user.id)
    return jsonify({'success': True, 'count': count})

# API route to delete a notification
@app.route('/notifications/delete/<int:notification_id>', methods=['POST'])
@login_required
def delete_notif(notification_id):
    success = delete_notification(notification_id)
    return jsonify({'success': success})

# API route to get unread notification count
@app.route('/api/notifications/count')
@login_required
def notification_count():
    count = get_unread_notification_count(current_user.id)
    return jsonify({'count': count})

# Add a datetime filter for Jinja templates
@app.template_filter('datetime')
def format_datetime(value, format='%Y-%m-%d %H:%M'):
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return value
    return value.strftime(format)

# Add date and time filters for Jinja templates
@app.template_filter('date')
def format_date(value):
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                value = datetime.strptime(value, '%Y-%m-%d')
            except ValueError:
                return value
    return value.strftime('%Y-%m-%d')

@app.template_filter('time')
def format_time(value):
    if value is None:
        return ""
    if isinstance(value, str):
        try:
            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                value = datetime.strptime(value, '%H:%M:%S')
            except ValueError:
                return value
    return value.strftime('%H:%M')

# API endpoint to record attendance
@app.route('/api/record_attendance', methods=['POST'])
def record_attendance():
    try:
        data = request.json

        if not data or 'student_id' not in data or 'lecture_id' not in data:
            return jsonify({'success': False, 'error': 'Invalid data'}), 400

        student_id = data.get('student_id')
        lecture_id = data.get('lecture_id')
        confidence = data.get('confidence', 0)

        # Get system settings - make sure to call the function
        settings = safe_get_settings()
        confidence_threshold = float(settings.get('confidence_threshold', 0.85))
        late_threshold_minutes = int(settings.get('late_threshold_minutes', 15))

        # Check if confidence score is above threshold
        if confidence < confidence_threshold:
            # Log low confidence attempt
            log_system_event(f"Low confidence attendance attempt: {confidence} for student ID {student_id}", "WARNING")
            return jsonify({'success': False, 'error': 'Face recognition confidence too low'}), 400

        # Get lecture details
        lecture = get_lecture_by_id(lecture_id)

        if not lecture:
            return jsonify({'success': False, 'error': 'Lecture not found'}), 404

        # Check if lecture is active
        if not lecture.get('is_active'):
            return jsonify({'success': False, 'error': 'Lecture is not active'}), 400

        # Check if student is already marked present
        if is_student_present(student_id, lecture_id):
            return jsonify({'success': False, 'error': 'Student already marked present'}), 400

        # Calculate if student is late
        lecture_start_time = datetime.strptime(lecture['start_time'], '%Y-%m-%d %H:%M:%S')
        current_time = datetime.now()
        time_diff = (current_time - lecture_start_time).total_seconds() / 60

        is_late = time_diff > late_threshold_minutes

        # Record attendance
        attendance_id = record_student_attendance(student_id, lecture_id, is_late, confidence)

        if attendance_id:
            # If student is late, create a notification
            if is_late:
                create_late_notification(student_id, lecture_id)

                # Get student details for notification
                student = get_student_by_id(student_id)

                # Notify lecturer if configured
                lecturer_id = lecture.get('lecturer_id')
                if lecturer_id:
                    lecturer = get_lecturer_by_id(lecturer_id)
                    if lecturer and lecturer.get('email_notifications') == 'true':
                        message = f"Student {student['name']} ({student['matric_number']}) arrived late to your {lecture['course_code']} lecture"
                        create_notification(lecturer['user_id'], message, 'late', lecture_id)

            return jsonify({'success': True, 'attendance_id': attendance_id, 'is_late': is_late})
        else:
            return jsonify({'success': False, 'error': 'Failed to record attendance'}), 500
    except Exception as e:
        import traceback
        traceback_str = traceback.format_exc()
        print(f"FULL TRACEBACK in record_attendance:\n{traceback_str}")
        app.logger.error(f"Error in record_attendance: {str(e)}\n{traceback_str}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/check_auth')
def check_auth():
    """A simple route to check authentication status"""
    if current_user.is_authenticated:
        return jsonify({
            'authenticated': True,
            'username': current_user.username,
            'role': current_user.role,
            'session_data': {k: session[k] for k in session if k != '_permanent'}
        })
    else:
        return jsonify({
            'authenticated': False,
        })

def debug_session():
    """Debug route to check session state"""
    output = {
        'is_authenticated': current_user.is_authenticated if hasattr(current_user, 'is_authenticated') else False,
        'session_data': {k: session[k] for k in session if k != '_permanent'},
        'current_user': {
            'id': current_user.id if hasattr(current_user, 'id') else None,
            'username': current_user.username if hasattr(current_user, 'username') else None,
            'role': current_user.role if hasattr(current_user, 'role') else None
        } if hasattr(current_user, 'id') else None
    }
    return jsonify(output)

@app.route('/test')
def test():
    """A simple test route that doesn't require authentication"""
    return jsonify({
        'message': 'Test route works!',
        'is_authenticated': current_user.is_authenticated if hasattr(current_user, 'is_authenticated') else False,
        'session_data': {k: session[k] for k in session if k != '_permanent'}
    })

@app.route('/debug_db')
def debug_db():
    """Debug route to check database contents"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            # Get all tables
            tables = [row['name'] for row in c.fetchall()]

            result = {'tables': {}}
            for table in tables:
                c.execute(f"SELECT * FROM {table} LIMIT 5")
                rows = [dict(row) for row in c.fetchall()]
                result['tables'][table] = rows

            # Get database path
            result['db_path'] = DB_PATH

            return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/debug_auth')
def debug_auth():
    """Debug route to check authentication status"""
    auth_status = {
        'is_authenticated': current_user.is_authenticated if hasattr(current_user, 'is_authenticated') else False,
        'user_id': getattr(current_user, 'id', None),
        'username': getattr(current_user, 'username', None),
        'role': getattr(current_user, 'role', None),
        'session': {k: session[k] for k in session if k != '_permanent'},
        'cookies': {k: v for k, v in request.cookies.items()},
        'db_path': DB_PATH
    }

    if hasattr(current_user, 'id'):
        try:
            with sqlite3.connect(DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                c = conn.cursor()
                c.execute("SELECT id, username, role FROM users WHERE id = ?", (current_user.id,))
                user_data = c.fetchone()

            if user_data:
                auth_status['db_user'] = dict(user_data)
            else:
                auth_status['db_user'] = None
        except Exception as e:
            auth_status['db_error'] = str(e)

    return jsonify(auth_status)

@app.route('/public')
def public():
    """A public route that doesn't require authentication"""
    return render_template('public.html')

@app.before_request
def check_authentication():
    """Check authentication status before each request"""
    if request.endpoint and request.endpoint != 'static':
        # Check for expired lectures
        ended_count = check_and_end_expired_lectures()
        if ended_count > 0:
            app.logger.info(f"Auto-ended {ended_count} expired lectures")

def check_and_end_expired_lectures():
    """Check for lectures that have exceeded their duration and end them"""
    try:
        # Get all active lectures
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()
            
            # Get active lectures with their start time and duration
            c.execute("""
                SELECT id, start_time, duration 
                FROM lectures 
                WHERE is_active = 1
            """)
            
            active_lectures = [dict(row) for row in c.fetchall()]
            
            now = datetime.now()
            ended_count = 0
            
            for lecture in active_lectures:
                # Calculate when the lecture should end
                start_time = datetime.strptime(lecture['start_time'], '%Y-%m-%d %H:%M:%S')
                duration_minutes = lecture['duration']
                end_time = start_time + timedelta(minutes=duration_minutes)
                
                # If current time is past the end time, end the lecture
                if now > end_time:
                    lecture_id = lecture['id']
                    app.logger.info(f"Auto-ending lecture {lecture_id} as duration has elapsed")
                    
                    # End the lecture
                    if update_lecture_status(lecture_id, is_active=False):
                        # Process absences
                        process_lecture_absences(lecture_id)
                        ended_count += 1
            
            return ended_count
    except Exception as e:
        app.logger.error(f"Error checking expired lectures: {str(e)}")
        return 0

# Add a function to generate CSRF tokens
def generate_csrf_token():
    if '_csrf_token' not in session:
        session['_csrf_token'] = os.urandom(24).hex()
    return session['_csrf_token']

# Add CSRF token to all templates
@app.context_processor
def inject_csrf_token():
    return dict(custom_csrf_token=generate_csrf_token())

def debug_get_system_settings():
    """Debug function to help identify where get_system_settings is being redefined"""
    import inspect
    import sys

    # Get all global variables in this module
    all_globals = globals()

    # Check if get_system_settings is in globals and what type it is
    if 'get_system_settings' in all_globals:
        gs = all_globals['get_system_settings']
        if isinstance(gs, str):
            print(f"Value: {gs}")
        elif callable(gs):
            print(f"It's callable, source: {inspect.getsource(gs)}")
    else:
        print("get_system_settings not found in globals")

    # Check all route functions for local variables named get_system_settings
    for name, obj in inspect.getmembers(sys.modules[__name__]):
        if inspect.isfunction(obj) and hasattr(obj, '__wrapped__'):
            try:
                source = inspect.getsource(obj)
                if "get_system_settings" in source and "=" in source:
                    print(f"Potential redefinition in function {name}:")
                    print(source)
            except:
                pass

# Call the debug function
debug_get_system_settings()

def check_and_fix_conflicting_settings():
    try:
        with sqlite3.connect(DB_PATH) as conn:
            c = conn.cursor()

            # List of keys that might conflict with Flask's built-in template variables
            conflicting_keys = ['csrf_token', 'request', 'session', 'g', 'url_for']

            for key in conflicting_keys:
                result = c.fetchone()

                if result:
                    new_key = f"app_{key}"
                    print(f"Found conflicting setting '{key}', renaming to '{new_key}'")
                    c.execute("UPDATE system_settings SET setting_key = ? WHERE setting_key = ?", (new_key, key))
                    conn.commit()

        return True
    except Exception as e:
        print(f"Error checking for conflicting settings: {e}")
        return False

def fix_csrf_token_in_database():
    """Directly fix the csrf_token entry in the database"""
    try:
        print("Attempting to fix csrf_token in database...")
        with sqlite3.connect(DB_PATH) as conn:
            c = conn.cursor()

            c.execute("SELECT setting_value FROM system_settings WHERE setting_key = 'csrf_token'")
            result = c.fetchone()

            if result:
                print(f"Found 'csrf_token' entry with value: {result[0]}")
                # Rename the entry
                c.execute("UPDATE system_settings SET setting_key = 'app_csrf_secret' WHERE setting_key = 'csrf_token'")
                conn.commit()
                print("Successfully renamed 'csrf_token' to 'app_csrf_secret' in database")
                return True
            else:
                return False
    except Exception as e:
        print(f"Error fixing csrf_token in database: {e}")
        return False

def get_all_departments():
    """Get a list of all departments from the database"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            # Get unique departments from students
            c.execute("SELECT DISTINCT department FROM students WHERE department IS NOT NULL AND department != ''")
            student_depts = [row['department'] for row in c.fetchall()]

            # Get unique departments from lecturers
            c.execute("SELECT DISTINCT department FROM lecturers WHERE department IS NOT NULL AND department != ''")
            lecturer_depts = [row['department'] for row in c.fetchall()]

            # Combine and remove duplicates
            all_departments = list(set(student_depts + lecturer_depts))
            all_departments.sort()

            return all_departments
    except Exception as e:
        app.logger.error(f"Error getting departments: {str(e)}")
        return []

def calculate_lecture_elapsed_time(lecture):
    """Calculate elapsed time for a lecture in minutes"""
    if not lecture:
        return 0
    
    try:
        start_time = datetime.strptime(lecture['start_time'], '%Y-%m-%d %H:%M:%S')
        if lecture.get('is_active'):
            # For active lectures, calculate against current time
            current_time = datetime.now()
            elapsed_seconds = (current_time - start_time).total_seconds()
        else:
            # For completed lectures, use end_time if available
            if lecture.get('end_time'):
                end_time = datetime.strptime(lecture['end_time'], '%Y-%m-%d %H:%M:%S')
                elapsed_seconds = (end_time - start_time).total_seconds()
            else:
                # Fallback to duration if end_time not available
                elapsed_seconds = lecture.get('duration_minutes', 0) * 60
        
        # Convert to minutes
        elapsed_minutes = int(elapsed_seconds / 60)
        return elapsed_minutes
    except Exception as e:
        print(f"Error calculating lecture elapsed time: {e}")
        return 0

# Global flag to control the background thread
lecture_checker_running = False

def lecture_checker_thread():
    """Background thread to periodically check for expired lectures"""
    global lecture_checker_running
    
    print("Starting lecture checker background thread")
    
    while lecture_checker_running:
        try:
            # Check every minute
            ended_count = check_and_end_expired_lectures()
            if ended_count > 0:
                print(f"Auto-ended {ended_count} expired lectures")
        except Exception as e:
            print(f"Error in lecture checker thread: {str(e)}")
        
        # Sleep for 1 minute
        time.sleep(60)
    
    print("Lecture checker background thread stopped")

if __name__ == '__main__':
    debug_get_system_settings()

    # Fix the csrf_token issue directly in the database
    fix_csrf_token_in_database()

    # Check for and fix any conflicting settings
    check_and_fix_conflicting_settings()

    # Initialize database
    init_db()
    
    # Add end_time column if it doesn't exist
    add_end_time_column()
    
    # Ensure there are courses in the database
    ensure_courses_exist()

    # Add default admin user if not exists
    try:
        if not get_user_by_username('admin'):
            # Add debug print
            print("Creating default admin user...")
            admin_password = generate_password_hash('admin123')
            # Changed from password_hash to password
            add_user('admin', admin_password, 'admin')
            print("Created default admin user: admin/admin123")
        else:
            print("Default admin user already exists")
    except Exception as e:
        print(f"Error creating default user: {e}")
    
    # Start the lecture checker thread
    lecture_checker_running = True
    lecture_checker = threading.Thread(target=lecture_checker_thread, daemon=True)
    lecture_checker.start()
    print("Started lecture checker background thread")

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    finally:
        # Stop the lecture checker thread when the app exits
        lecture_checker_running = False
        if 'lecture_checker' in locals():
            lecture_checker.join(timeout=2)
            print("Stopped lecture checker background thread")




















