@echo off
echo ========================================
echo  Biometric Attendance System
echo  Production Control Center
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Check if required packages are installed
echo Checking dependencies...
python -c "import PyQt5, psutil, sqlite3" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install PyQt5 psutil
    if errorlevel 1 (
        echo ERROR: Failed to install required packages
        pause
        exit /b 1
    )
)

REM Change to app directory
cd /d "%~dp0"

REM Start the Production Control Center
echo Starting Production Control Center...
echo.
python production_control_center.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ERROR: Control Center exited with an error
    pause
)
