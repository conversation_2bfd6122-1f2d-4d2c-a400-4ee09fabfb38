import os
import cv2
import face_recognition
import numpy as np
import pickle
from collections import Counter
from datetime import datetime

# === CONFIGURATION ===
BASE_DIR = "./images/student_faces"
CACHE_PATH = "encodings_cache.pkl"
SUPPORTED_EXTENSIONS = (".jpg", ".jpeg", ".png")


def load_known_faces(student_ids=None, use_database=True):
    """
    Loads face encodings from database first, then images folder as fallback.

    Args:
        student_ids: List of specific student IDs to load (None for all)
        use_database: Whether to load from database first (default: True)

    Returns:
        tuple: (known_encodings, known_names, student_id_map)
    """
    known_encodings = []
    known_names = []
    student_id_map = {}  # Maps name to student_id for attendance recording

    # === Try loading from database first ===
    if use_database:
        try:
            import sqlite3
            from db import DB_PATH

            with sqlite3.connect(DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                c = conn.cursor()

                if student_ids:
                    # Load specific students
                    placeholders = ','.join(['?'] * len(student_ids))
                    query = f"""
                        SELECT id, name, matric_number, face_encoding
                        FROM students
                        WHERE id IN ({placeholders}) AND face_encoding IS NOT NULL
                    """
                    c.execute(query, student_ids)
                else:
                    # Load all students with face encodings
                    c.execute("""
                        SELECT id, name, matric_number, face_encoding
                        FROM students
                        WHERE face_encoding IS NOT NULL
                    """)

                db_students = c.fetchall()

                for student in db_students:
                    try:
                        # Deserialize face encoding
                        face_encoding = pickle.loads(student['face_encoding'])
                        known_encodings.append(face_encoding)

                        # Use name as identifier (consistent with image loading)
                        name = student['name'].strip().lower()
                        known_names.append(name)
                        student_id_map[name] = student['id']

                    except Exception as e:
                        print(f"⚠️ Failed to load encoding for {student['name']}: {e}")

                if known_encodings:
                    print(f"✅ Loaded {len(known_encodings)} face encodings from database")
                    return known_encodings, known_names, student_id_map
                else:
                    print("⚠️ No face encodings found in database, falling back to images")

        except Exception as e:
            print(f"⚠️ Database loading failed: {e}, falling back to images")

    # === Fallback to image loading ===
    # === Try loading cache ===
    if os.path.exists(CACHE_PATH):
        try:
            with open(CACHE_PATH, 'rb') as f:
                cache = pickle.load(f)
                print("✅ Loaded encodings from cache.")
                # Create student_id_map from names (will need to be mapped later)
                for name in cache['names']:
                    student_id_map[name] = None  # Will be resolved during attendance
                return cache['encodings'], cache['names'], student_id_map
        except Exception as e:
            print(f"⚠️ Failed to load cache: {e} - falling back to full scan.")

    if not os.path.exists(BASE_DIR):
        print(f"⚠️ Face directory not found: {BASE_DIR}")
        return [], [], {}

    for student_name in os.listdir(BASE_DIR):
        student_folder = os.path.join(BASE_DIR, student_name)
        if not os.path.isdir(student_folder):
            continue

        student_name = student_name.strip().lower().replace("_", " ")

        for filename in os.listdir(student_folder):
            if not filename.lower().endswith(SUPPORTED_EXTENSIONS):
                continue

            path = os.path.join(student_folder, filename)
            img = cv2.imread(path)

            if img is None:
                print(f"❌ Failed to read image: {path}")
                continue

            rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            boxes = face_recognition.face_locations(rgb)

            if len(boxes) != 1:
                print(f"⚠️ Skipping {filename} - found {len(boxes)} faces")
                continue

            encoding = face_recognition.face_encodings(rgb, boxes)[0]
            known_encodings.append(encoding)
            known_names.append(student_name)
            student_id_map[student_name] = None  # Will be resolved during attendance

    # === Save cache ===
    try:
        with open(CACHE_PATH, 'wb') as f:
            pickle.dump({
                'encodings': known_encodings,
                'names': known_names,
                'timestamp': datetime.now()
            }, f)
        print(f"🗃️ Encodings cached to {CACHE_PATH}")
    except Exception as e:
        print(f"⚠️ Could not save cache: {e}")

    # === Log summary ===
    summary = Counter(known_names)
    for name, count in summary.items():
        print(f"🧑‍🎓 {name.title()}: {count} image(s) encoded")
    print(f"✅ Loaded {len(known_encodings)} total encodings from {len(summary)} students.")

    return known_encodings, known_names, student_id_map


def load_lecturer_faces():
    """
    Loads face encodings specifically for lecturers.
    """
    lecturer_dir = "./images/lecturer_faces"
    known_encodings = []
    known_names = []

    if not os.path.exists(lecturer_dir):
        print(f"⚠️ Lecturer face directory not found: {lecturer_dir}")
        return [], []

    for lecturer_folder in os.listdir(lecturer_dir):
        folder_path = os.path.join(lecturer_dir, lecturer_folder)
        if not os.path.isdir(folder_path):
            continue

        # Read metadata
        metadata_path = os.path.join(folder_path, "metadata.txt")
        if not os.path.exists(metadata_path):
            print(f"⚠️ Skipping {folder_path}: No metadata.txt found")
            continue

        metadata = {}
        with open(metadata_path, 'r') as f:
            for line in f:
                if ':' in line:
                    key, value = line.split(':', 1)
                    metadata[key.strip()] = value.strip()

        name = metadata.get('Name')
        if not name:
            print(f"⚠️ Skipping {folder_path}: Missing name in metadata")
            continue

        # Find RGB images
        rgb_images = [f for f in os.listdir(folder_path) if f.endswith(SUPPORTED_EXTENSIONS) and 'rgb' in f]
        if not rgb_images:
            print(f"⚠️ Skipping {folder_path}: No RGB images found")
            continue

        # Process the first RGB image
        image_path = os.path.join(folder_path, rgb_images[0])
        img = cv2.imread(image_path)

        if img is None:
            print(f"❌ Failed to read image: {image_path}")
            continue

        rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        boxes = face_recognition.face_locations(rgb)

        if len(boxes) != 1:
            print(f"⚠️ Skipping {image_path} - found {len(boxes)} faces")
            continue

        encoding = face_recognition.face_encodings(rgb, boxes)[0]
        known_encodings.append(encoding)
        known_names.append(name.lower())

    # Log summary
    summary = Counter(known_names)
    for name, count in summary.items():
        print(f"👨‍🏫 {name.title()}: {count} image(s) encoded")
    print(f"✅ Loaded {len(known_encodings)} total encodings from {len(summary)} lecturers.")

    return known_encodings, known_names
