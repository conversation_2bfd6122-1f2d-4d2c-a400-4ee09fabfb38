import os
import cv2
import face_recognition
import numpy as np
import pickle
from collections import Counter
from datetime import datetime

# === CONFIGURATION ===
BASE_DIR = "./images/student_faces"
CACHE_PATH = "encodings_cache.pkl"
SUPPORTED_EXTENSIONS = (".jpg", ".jpeg", ".png")


def load_known_faces():
    """
    Loads face encodings from RGB and IR images per student folder,
    with optional caching to speed up repeated loads.
    """
    # === Try loading cache ===
    if os.path.exists(CACHE_PATH):
        try:
            with open(CACHE_PATH, 'rb') as f:
                cache = pickle.load(f)
                print("✅ Loaded encodings from cache.")
                return cache['encodings'], cache['names']
        except Exception as e:
            print(f"⚠️ Failed to load cache: {e} - falling back to full scan.")

    known_encodings = []
    known_names = []

    if not os.path.exists(BASE_DIR):
        print(f"⚠️ Face directory not found: {BASE_DIR}")
        return [], []

    for student_name in os.listdir(BASE_DIR):
        student_folder = os.path.join(BASE_DIR, student_name)
        if not os.path.isdir(student_folder):
            continue

        student_name = student_name.strip().lower().replace("_", " ")

        for filename in os.listdir(student_folder):
            if not filename.lower().endswith(SUPPORTED_EXTENSIONS):
                continue

            path = os.path.join(student_folder, filename)
            img = cv2.imread(path)

            if img is None:
                print(f"❌ Failed to read image: {path}")
                continue

            rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            boxes = face_recognition.face_locations(rgb)

            if len(boxes) != 1:
                print(f"⚠️ Skipping {filename} - found {len(boxes)} faces")
                continue

            encoding = face_recognition.face_encodings(rgb, boxes)[0]
            known_encodings.append(encoding)
            known_names.append(student_name)

    # === Save cache ===
    try:
        with open(CACHE_PATH, 'wb') as f:
            pickle.dump({
                'encodings': known_encodings,
                'names': known_names,
                'timestamp': datetime.now()
            }, f)
        print(f"🗃️ Encodings cached to {CACHE_PATH}")
    except Exception as e:
        print(f"⚠️ Could not save cache: {e}")

    # === Log summary ===
    summary = Counter(known_names)
    for name, count in summary.items():
        print(f"🧑‍🎓 {name.title()}: {count} image(s) encoded")
    print(f"✅ Loaded {len(known_encodings)} total encodings from {len(summary)} students.")

    return known_encodings, known_names


def load_lecturer_faces():
    """
    Loads face encodings specifically for lecturers.
    """
    lecturer_dir = "./images/lecturer_faces"
    known_encodings = []
    known_names = []
    
    if not os.path.exists(lecturer_dir):
        print(f"⚠️ Lecturer face directory not found: {lecturer_dir}")
        return [], []
        
    for lecturer_folder in os.listdir(lecturer_dir):
        folder_path = os.path.join(lecturer_dir, lecturer_folder)
        if not os.path.isdir(folder_path):
            continue
            
        # Read metadata
        metadata_path = os.path.join(folder_path, "metadata.txt")
        if not os.path.exists(metadata_path):
            print(f"⚠️ Skipping {folder_path}: No metadata.txt found")
            continue
            
        metadata = {}
        with open(metadata_path, 'r') as f:
            for line in f:
                if ':' in line:
                    key, value = line.split(':', 1)
                    metadata[key.strip()] = value.strip()
        
        name = metadata.get('Name')
        if not name:
            print(f"⚠️ Skipping {folder_path}: Missing name in metadata")
            continue
            
        # Find RGB images
        rgb_images = [f for f in os.listdir(folder_path) if f.endswith(SUPPORTED_EXTENSIONS) and 'rgb' in f]
        if not rgb_images:
            print(f"⚠️ Skipping {folder_path}: No RGB images found")
            continue
            
        # Process the first RGB image
        image_path = os.path.join(folder_path, rgb_images[0])
        img = cv2.imread(image_path)
        
        if img is None:
            print(f"❌ Failed to read image: {image_path}")
            continue
            
        rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        boxes = face_recognition.face_locations(rgb)
        
        if len(boxes) != 1:
            print(f"⚠️ Skipping {image_path} - found {len(boxes)} faces")
            continue
            
        encoding = face_recognition.face_encodings(rgb, boxes)[0]
        known_encodings.append(encoding)
        known_names.append(name.lower())
    
    # Log summary
    summary = Counter(known_names)
    for name, count in summary.items():
        print(f"👨‍🏫 {name.title()}: {count} image(s) encoded")
    print(f"✅ Loaded {len(known_encodings)} total encodings from {len(summary)} lecturers.")
    
    return known_encodings, known_names
