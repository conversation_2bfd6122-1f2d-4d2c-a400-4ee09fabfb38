from db import init_db, import_faces_to_db
from logger import log_system_event
import argparse
import threading
import time
import os

def start_socket_server():
    from socket_server import start_server
    start_server()

def start_capture_app():
    import kinect_capture_app

def start_voice_listener():
    from voice_listener import listen_for_wake_word
    listen_for_wake_word()

def start_web_portal():
    from web_portal import app
    # Use environment variable for port if available, otherwise default to 5000
    port = int(os.environ.get('PORT', 5000))
    # Run in debug mode only if explicitly set in environment
    debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Biometric Attendance System')
    parser.add_argument('--mode', choices=['server', 'capture', 'voice', 'web', 'all'],
                        default='server', help='Mode to run the application in')
    parser.add_argument('--import-faces', action='store_true',
                        help='Import existing faces to the database')
    parser.add_argument('--port', type=int, default=5000,
                        help='Port for the web portal (default: 5000)')

    args = parser.parse_args()

    # Set port environment variable if specified
    if args.port != 5000:
        os.environ['PORT'] = str(args.port)

    # Initialize database
    log_system_event("Initializing database...")
    init_db()

    # Import faces if requested
    if args.import_faces:
        log_system_event("Importing faces to database...")
        import_faces_to_db()

    # Start requested components
    if args.mode == 'server' or args.mode == 'all':
        log_system_event("Starting socket server...")
        if args.mode == 'all':
            threading.Thread(target=start_socket_server, daemon=True).start()
        else:
            start_socket_server()

    if args.mode == 'capture' or args.mode == 'all':
        log_system_event("Starting capture application...")
        if args.mode == 'all':
            threading.Thread(target=start_capture_app, daemon=True).start()
        else:
            start_capture_app()

    if args.mode == 'voice' or args.mode == 'all':
        log_system_event("Starting voice listener...")
        if args.mode == 'all':
            threading.Thread(target=start_voice_listener, daemon=True).start()
        else:
            start_voice_listener()

    if args.mode == 'web' or args.mode == 'all':
        log_system_event("Starting web portal...")
        if args.mode == 'all':
            threading.Thread(target=start_web_portal, daemon=True).start()
        else:
            start_web_portal()

    # Keep main thread alive if running multiple components
    if args.mode == 'all':
        try:
            log_system_event("All components started. Press Ctrl+C to exit.")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            log_system_event("System shutdown requested. Exiting...")
