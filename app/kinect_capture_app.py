import os
import cv2
import numpy as np
import sys
import threading
import socket
import struct
import time
import gzip
import pyttsx3
import shutil
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QLineEdit,
                             QPushButton, QRadioButton, QCheckBox, QVBoxLayout,
                             QHBoxLayout, QWidget, QFrame, QMessageBox,
                             QFileDialog, QProgressBar, QGroupBox, QStatusBar)
from PyQt5.QtGui import QPixmap, QImage, QFont, QIcon
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSize

# === Configuration for Network Stream ===
HOST = '0.0.0.0'  # Listen on all interfaces
PORT = 9090
MAX_FRAME_SIZE = 15 * 1024 * 1024  # 15 MB
TIMEOUT_SECONDS = 30  # Socket timeout for receiving data
HEADER_SIZE = 20  # Using 20 bytes (5 integers) for header

# Add a dictionary to track connected cameras
connected_cameras = {}

# Add these constants to match the C# downscaling
IR_DOWNSCALE_FACTOR = 2
DEPTH_DOWNSCALE_FACTOR = 2

FRAME_DIMS = {
    "RGB_WIDTH": 1920,
    "RGB_HEIGHT": 1080,
    "IR_WIDTH": 512,
    "IR_HEIGHT": 424,
    "DEPTH_WIDTH": 512,
    "DEPTH_HEIGHT": 424
}

SAVE_DIRS = {
    "Student": "images/student_faces",
    "Lecturer": "images/lecturer_faces"
}

# Create both directories
for directory in SAVE_DIRS.values():
    os.makedirs(directory, exist_ok=True)

# Initialize text-to-speech engine
engine = pyttsx3.init()
engine.setProperty("rate", 170)

# App constants
APP_VERSION = "1.0.0"
APP_TITLE = f"Kinect Student Capture v{APP_VERSION}"

# Global variables
capture_requested = False
glasses_mode = False
current_person_folder = ""
current_metadata = {}
current_rgb_frame = None
current_ir_frame = None
current_depth_frame = None
server_socket = None
camera_connected_event = threading.Event()  # Signals when camera connection is established


def speak(text):
    """Uses text-to-speech to announce messages."""
    engine.say(text)
    engine.runAndWait()


def receive_all(sock, n):
    """Receive exactly n bytes from the socket."""
    data = bytearray()
    bytes_received = 0

    print(f"[Socket] receive_all: Attempting to receive {n} bytes")

    while bytes_received < n:
        try:
            packet = sock.recv(n - bytes_received)
            if not packet:
                print(f"[Socket] receive_all: Connection closed after receiving {bytes_received}/{n} bytes")
                return None

            data.extend(packet)
            bytes_received += len(packet)

            # Log progress for large transfers
            if n > 1000:
                print(f"[Socket] receive_all: Received {bytes_received}/{n} bytes ({bytes_received/n*100:.1f}%)")

        except socket.timeout:
            print(f"[Socket] receive_all: Timeout after receiving {bytes_received}/{n} bytes")
            return None
        except ConnectionResetError:
            print(f"[Socket] receive_all: Connection reset after receiving {bytes_received}/{n} bytes")
            return None
        except Exception as e:
            print(f"[Socket] receive_all: Error receiving data: {e}")
            return None

    print(f"[Socket] receive_all: Successfully received all {n} bytes")
    return data


def normalize_ir(ir_frame):
    """
    Enhances IR image for better visibility, mimicking Microsoft's SDK output.
    Avoids excessive contrast and preserves more detail.
    """
    try:
        # Convert to float32 and normalize to 0-1 range
        ir_normalized = ir_frame.astype(np.float32) / 65535.0

        # Apply a more moderate amplification
        amplified = np.clip(ir_normalized * 1.5, 0.0, 1.0)

        # Apply gamma correction with a more moderate gamma value
        # Higher gamma value (closer to 1.0) will reduce contrast
        gamma_corrected = np.power(amplified, 0.5)  # Using 0.5 instead of 0.32 for less contrast

        # Convert back to uint8 (0-255 range)
        ir_enhanced = np.uint8(gamma_corrected * 255)

        # Skip histogram equalization to avoid excessive contrast

        # Apply a very slight Gaussian blur to reduce noise while preserving detail
        ir_enhanced = cv2.GaussianBlur(ir_enhanced, (3, 3), 0.5)

        return ir_enhanced
    except Exception as e:
        print(f"[GUI] Error normalizing IR frame: {e}")
        # Return original frame converted to uint8 if enhancement fails
        return cv2.convertScaleAbs(ir_frame, alpha=(255.0/65535.0))


def normalize_depth(depth_frame):
    """Normalizes depth data for visualization."""
    # Convert to float32 for processing
    depth_float = depth_frame.astype(np.float32)

    # Filter out invalid values (0 or very large values)
    valid_mask = (depth_float > 0) & (depth_float < 8000)

    if np.any(valid_mask):
        # Get min/max of valid values only
        valid_min = np.min(depth_float[valid_mask])
        valid_max = np.max(depth_float[valid_mask])

        # Create normalized image (0-255)
        normalized = np.zeros_like(depth_float)
        if valid_max > valid_min:
            # Invert so closer is brighter
            normalized[valid_mask] = 255 - ((depth_float[valid_mask] - valid_min) * 255.0 / (valid_max - valid_min))
    else:
        normalized = np.zeros_like(depth_float)

    return normalized.astype(np.uint8)


def colorize_depth(depth_normalized):
    """Applies a colormap to normalized depth data."""
    # Apply a colormap (COLORMAP_JET is common for depth visualization)
    depth_colormap = cv2.applyColorMap(depth_normalized, cv2.COLORMAP_JET)

    # Create a mask for invalid values (0 in the normalized image)
    invalid_mask = (depth_normalized == 0)

    # Set invalid values to black
    depth_colormap[invalid_mask] = [0, 0, 0]

    return depth_colormap


# Socket receiver thread
class KinectReceiverThread(QThread):
    frame_received = pyqtSignal(dict)
    camera_connected = pyqtSignal(str)
    camera_disconnected = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = True

    def run(self):
        global server_socket, connected_cameras, camera_connected_event

        try:
            # Create and configure server socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.settimeout(1.0)  # Short timeout for accept() to allow clean shutdown
            server_socket.bind((HOST, PORT))
            server_socket.listen(5)

            print(f"[Socket] Server listening on {HOST}:{PORT}")
            camera_connected_event.set()  # Signal that the server is ready

            while self.running:
                try:
                    # Accept new connections
                    client_socket, client_address = server_socket.accept()
                    client_socket.settimeout(TIMEOUT_SECONDS)
                    print(f"[Socket] Accepted connection from {client_address}")

                    # Start a new thread to handle this client
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address),
                        daemon=True
                    )
                    client_thread.start()

                except socket.timeout:
                    # This is expected due to the timeout on accept()
                    continue
                except Exception as e:
                    if self.running:  # Only report errors if we're supposed to be running
                        print(f"[Socket] Accept error: {e}")
                        self.error_occurred.emit(f"Socket accept error: {e}")
                    break

        except Exception as e:
            print(f"[Socket] Server error: {e}")
            self.error_occurred.emit(f"Socket server error: {e}")
        finally:
            if server_socket:
                try:
                    server_socket.close()
                except:
                    pass

    def handle_client(self, client_socket, client_address):
        """Handle incoming Kinect client connection."""
        global current_rgb_frame, current_ir_frame, current_depth_frame, connected_cameras

        print(f"[Socket] New connection from {client_address}")

        # Set a timeout to detect stalled connections
        client_socket.settimeout(5.0)

        try:
            while True:
                # Receive header (20 bytes)
                print(f"[Socket] Waiting for header from {client_address}")
                header_data = receive_all(client_socket, 20)

                if header_data is None:
                    print(f"[Socket] Failed to receive header from {client_address}")
                    break

                # Unpack header using little-endian byte order
                try:
                    seq, rgb_size, ir_size, depth_size, camera_id = struct.unpack("<IIIII", header_data)
                    print(f"[Socket] Unpacked header: seq={seq}, rgb_size={rgb_size}, ir_size={ir_size}, depth_size={depth_size}, camera_id={camera_id}")
                except struct.error as e:
                    print(f"[Socket] Error unpacking header: {e}")
                    print(f"[Socket] Header bytes: {' '.join(f'{b:02x}' for b in header_data)}")
                    break

                # Validate frame sizes to prevent memory issues
                MAX_FRAME_SIZE = 15 * 1024 * 1024  # 15MB max
                if (rgb_size > MAX_FRAME_SIZE or ir_size > MAX_FRAME_SIZE or depth_size > MAX_FRAME_SIZE or
                    rgb_size <= 0 or ir_size <= 0 or depth_size <= 0):
                    print(f"[Socket] Invalid frame sizes: rgb_size={rgb_size}, ir_size={ir_size}, depth_size={depth_size}")
                    # Try to send error acknowledgment
                    try:
                        client_socket.sendall(b'\x00')
                    except:
                        pass
                    break

                # Update connected cameras dictionary
                if camera_id not in connected_cameras:
                    connected_cameras[camera_id] = {
                        'address': client_address,
                        'last_frame_time': datetime.now(),
                        'frame_count': 0
                    }
                    print(f"[Socket] Camera {camera_id} connected from {client_address}")

                # Receive RGB frame
                print(f"[Socket] Receiving RGB data ({rgb_size} bytes)")
                rgb_data = receive_all(client_socket, rgb_size)
                if rgb_data is None:
                    print(f"[Socket] Failed to receive RGB data")
                    break

                # Receive IR frame
                print(f"[Socket] Receiving IR data ({ir_size} bytes)")
                ir_data = receive_all(client_socket, ir_size)
                if ir_data is None:
                    print(f"[Socket] Failed to receive IR data")
                    break

                # Receive Depth frame
                print(f"[Socket] Receiving depth data ({depth_size} bytes)")
                depth_data = receive_all(client_socket, depth_size)
                if depth_data is None:
                    print(f"[Socket] Failed to receive depth data")
                    break

                # Process frames
                try:
                    # Process RGB frame
                    rgb_frame = cv2.imdecode(np.frombuffer(rgb_data, dtype=np.uint8), cv2.IMREAD_COLOR)
                    if rgb_frame is not None:
                        current_rgb_frame = rgb_frame
                        print(f"[Socket] RGB frame decoded successfully: {rgb_frame.shape}")

                    # Process IR frame
                    ir_array = np.frombuffer(ir_data, dtype=np.uint16)
                    ir_frame = ir_array.reshape(FRAME_DIMS["IR_HEIGHT"], FRAME_DIMS["IR_WIDTH"])
                    current_ir_frame = ir_frame
                    print(f"[Socket] IR frame decoded successfully: {ir_frame.shape}")

                    # Process Depth frame
                    depth_array = np.frombuffer(depth_data, dtype=np.uint16)
                    depth_frame = depth_array.reshape(FRAME_DIMS["DEPTH_HEIGHT"], FRAME_DIMS["DEPTH_WIDTH"])
                    current_depth_frame = depth_frame
                    print(f"[Socket] Depth frame decoded successfully: {depth_frame.shape}")

                    # Update frame count and timestamp
                    connected_cameras[camera_id]['last_frame_time'] = datetime.now()
                    connected_cameras[camera_id]['frame_count'] += 1

                    # Send acknowledgment
                    client_socket.sendall(b'\x01')
                    print(f"[Socket] Acknowledgment sent for frame {seq}")

                except Exception as e:
                    print(f"[Socket] Error processing frames: {e}")
                    import traceback
                    traceback.print_exc()
                    # Try to send error acknowledgment
                    try:
                        client_socket.sendall(b'\x00')
                    except:
                        pass
                    break

        except Exception as e:
            print(f"[Socket] Error handling client {client_address}: {e}")
        finally:
            print(f"[Socket] Connection closed from {client_address}")
            client_socket.close()
            if camera_id in connected_cameras:
                del connected_cameras[camera_id]
            self.camera_disconnected.emit(f"Camera {camera_id} disconnected")
            print(f"[Socket] Camera {camera_id} disconnected")

    def stop(self):
        self.running = False


# Countdown timer class
class CountdownTimer(QWidget):
    finished = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Create layout
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create countdown label
        self.label = QLabel("3")
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setStyleSheet(
            "font-size: 72px; font-weight: bold; color: white; background-color: rgba(0, 0, 0, 128); border-radius: 10px;")
        layout.addWidget(self.label)

        # Set size
        self.setFixedSize(200, 200)

        # Initialize timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_countdown)
        self.count = 3

    def start_countdown(self):
        self.count = 3
        self.label.setText(str(self.count))
        self.timer.start(1000)

    def update_countdown(self):
        self.count -= 1
        if self.count > 0:
            self.label.setText(str(self.count))
        else:
            self.timer.stop()
            self.hide()
            self.finished.emit()


# Loading overlay class
class LoadingOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Create layout
        layout = QVBoxLayout()
        self.setLayout(layout)

        # Create message label
        self.message = QLabel("Processing...")
        self.message.setAlignment(Qt.AlignCenter)
        self.message.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        layout.addWidget(self.message)

        # Create progress bar
        self.progress = QProgressBar()
        self.progress.setRange(0, 0)  # Indeterminate progress
        self.progress.setTextVisible(False)
        self.progress.setStyleSheet(
            "QProgressBar {border: 1px solid gray; border-radius: 3px; background: rgba(0, 0, 0, 128);} QProgressBar::chunk {background-color: #3366CC;}")
        layout.addWidget(self.progress)

        # Set background
        self.setStyleSheet("background-color: rgba(0, 0, 0, 180); border-radius: 10px;")

        # Set size
        self.setFixedSize(300, 100)

    def set_message(self, message):
        self.message.setText(message)


# Main application window
class KinectCaptureApp(QMainWindow):
    def __init__(self):
        super().__init__()

        # Initialize variables
        self.name = ""
        self.id_number = ""
        self.selected_role = "Student"
        self.use_glasses_mode = False

        # Set up the UI
        self.init_ui()

        # Create countdown timer
        self.countdown = CountdownTimer(self)
        self.countdown.finished.connect(self.trigger_capture)

        # Create loading overlay
        self.loading = LoadingOverlay(self)

        # Start Kinect receiver thread
        self.receiver_thread = KinectReceiverThread()
        self.receiver_thread.frame_received.connect(self.process_frames)
        self.receiver_thread.camera_connected.connect(self.on_camera_connected)
        self.receiver_thread.camera_disconnected.connect(self.on_camera_disconnected)
        self.receiver_thread.error_occurred.connect(self.on_error)
        self.receiver_thread.start()

        # Start frame update timer - increase frame rate to 60 FPS
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)
        self.update_timer.start(16)  # ~60 FPS (1000ms / 60 = 16.67ms)

    def init_ui(self):
        # Set window properties
        self.setWindowTitle(APP_TITLE)
        self.setMinimumSize(1200, 700)

        # Try to set window icon
        try:
            icon_path = "./assets/icon.ico"
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)

        # Create left frame for video displays
        left_frame = QWidget()
        left_layout = QVBoxLayout(left_frame)
        main_layout.addWidget(left_frame, 2)  # 2:1 ratio with right frame

        # RGB video display
        rgb_group = QGroupBox("RGB Camera")
        rgb_layout = QVBoxLayout(rgb_group)
        self.rgb_display = QLabel("Waiting for RGB Feed...")
        self.rgb_display.setAlignment(Qt.AlignCenter)
        self.rgb_display.setMinimumSize(640, 360)
        self.rgb_display.setStyleSheet("background-color: black; color: white;")
        rgb_layout.addWidget(self.rgb_display)
        left_layout.addWidget(rgb_group)

        # IR video display
        ir_group = QGroupBox("IR Camera")
        ir_layout = QVBoxLayout(ir_group)
        self.ir_display = QLabel("Waiting for IR Feed...")
        self.ir_display.setAlignment(Qt.AlignCenter)
        self.ir_display.setMinimumSize(320, 240)
        self.ir_display.setStyleSheet("background-color: black; color: white;")
        ir_layout.addWidget(self.ir_display)
        left_layout.addWidget(ir_group)

        # Create right frame for controls
        right_frame = QWidget()
        right_layout = QVBoxLayout(right_frame)
        main_layout.addWidget(right_frame, 1)  # 1:2 ratio with left frame

        # App title
        title_label = QLabel(APP_TITLE)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #3366CC;")
        right_layout.addWidget(title_label)

        # Input form
        form_group = QGroupBox("Registration Form")
        form_layout = QVBoxLayout(form_group)

        # Name input
        name_layout = QHBoxLayout()
        name_label = QLabel("👤 Student Name:")
        self.name_label = name_label  # Store reference for updating
        name_layout.addWidget(name_label)
        self.name_input = QLineEdit()
        name_layout.addWidget(self.name_input)
        form_layout.addLayout(name_layout)

        # ID input
        id_layout = QHBoxLayout()
        id_label = QLabel("🆔 Matric No:")
        self.id_label = id_label  # Store reference for updating
        id_layout.addWidget(id_label)
        self.id_input = QLineEdit()
        id_layout.addWidget(self.id_input)
        form_layout.addLayout(id_layout)

        # Role selection
        role_layout = QHBoxLayout()
        role_layout.addWidget(QLabel("🧾 Role:"))
        self.student_radio = QRadioButton("Student")
        self.student_radio.setChecked(True)
        self.student_radio.toggled.connect(self.on_role_changed)
        role_layout.addWidget(self.student_radio)
        self.lecturer_radio = QRadioButton("Lecturer")
        self.lecturer_radio.toggled.connect(self.on_role_changed)
        role_layout.addWidget(self.lecturer_radio)
        form_layout.addLayout(role_layout)

        # Glasses checkbox
        self.glasses_checkbox = QCheckBox("👓 Use Glasses Mode (Students Only)")
        form_layout.addWidget(self.glasses_checkbox)

        right_layout.addWidget(form_group)

        # Action buttons
        buttons_group = QGroupBox("Actions")
        buttons_layout = QVBoxLayout(buttons_group)

        # Add New Face button
        self.add_face_btn = QPushButton("📸 Add New Face")
        self.add_face_btn.clicked.connect(self.add_new_face)
        buttons_layout.addWidget(self.add_face_btn)

        # Manage Faces button
        self.manage_faces_btn = QPushButton("🗂️ Manage Faces")
        self.manage_faces_btn.clicked.connect(self.manage_faces)
        buttons_layout.addWidget(self.manage_faces_btn)

        # Delete Face button
        self.delete_face_btn = QPushButton("❌ Delete Face")
        self.delete_face_btn.clicked.connect(self.delete_face)
        buttons_layout.addWidget(self.delete_face_btn)

        # Reset Form button
        self.reset_form_btn = QPushButton("🔄 Reset Form")
        self.reset_form_btn.clicked.connect(self.reset_form)
        buttons_layout.addWidget(self.reset_form_btn)

        right_layout.addWidget(buttons_group)

        # Help and About buttons
        help_about_layout = QHBoxLayout()

        # Help button
        self.help_btn = QPushButton("❓ Help")
        self.help_btn.clicked.connect(self.show_help)
        help_about_layout.addWidget(self.help_btn)

        # About button
        self.about_btn = QPushButton("ℹ️ About")
        self.about_btn.clicked.connect(self.show_about)
        help_about_layout.addWidget(self.about_btn)

        right_layout.addLayout(help_about_layout)

        # Exit button
        self.exit_btn = QPushButton("🚪 Exit Application")
        self.exit_btn.clicked.connect(self.close)
        self.exit_btn.setStyleSheet("background-color: #E74C3C; color: white;")
        right_layout.addWidget(self.exit_btn)

        # Add status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready. Waiting for Kinect connection...")

        # Set keyboard shortcuts
        self.add_face_btn.setShortcut("Ctrl+A")
        self.manage_faces_btn.setShortcut("Ctrl+M")
        self.delete_face_btn.setShortcut("Ctrl+D")
        self.reset_form_btn.setShortcut("Ctrl+R")
        self.help_btn.setShortcut("Ctrl+H")
        self.exit_btn.setShortcut("Esc")

    def on_role_changed(self):
        """Updates the ID label text and name label text based on the selected role."""
        if self.student_radio.isChecked():
            self.selected_role = "Student"
            self.id_label.setText("🆔 Matric No:")
            self.name_label.setText("👤 Student Name:")
        else:
            self.selected_role = "Lecturer"
            self.id_label.setText("🆔 Staff ID:")
            self.name_label.setText("👤 Staff Name:")

    def process_frames(self, frames):
        """Process received frames from the Kinect."""
        global current_rgb_frame, current_ir_frame, current_depth_frame

        if 'rgb' in frames:
            current_rgb_frame = frames['rgb']

        if 'ir' in frames:
            current_ir_frame = frames['ir']

        if 'depth' in frames:
            current_depth_frame = frames['depth']

    def update_displays(self):
        """Updates the video displays with the latest frames."""
        global current_rgb_frame, current_ir_frame, current_depth_frame, capture_requested

        # Update RGB display
        if current_rgb_frame is not None:
            try:
                # Get display dimensions
                rgb_display_width = self.rgb_display.width()
                rgb_display_height = self.rgb_display.height()

                # Calculate aspect ratio
                rgb_aspect = FRAME_DIMS["RGB_WIDTH"] / FRAME_DIMS["RGB_HEIGHT"]
                display_aspect = rgb_display_width / rgb_display_height

                # Calculate dimensions that preserve aspect ratio
                if display_aspect > rgb_aspect:
                    # Display is wider than frame
                    target_height = rgb_display_height
                    target_width = int(target_height * rgb_aspect)
                else:
                    # Display is taller than frame
                    target_width = rgb_display_width
                    target_height = int(target_width / rgb_aspect)

                # Resize RGB frame while preserving aspect ratio
                rgb_resized = cv2.resize(current_rgb_frame, (target_width, target_height))
                rgb_color = cv2.cvtColor(rgb_resized, cv2.COLOR_BGR2RGB)

                # Convert to QImage and then QPixmap
                h, w, ch = rgb_color.shape
                bytes_per_line = ch * w
                rgb_qimg = QImage(rgb_color.data, w, h, bytes_per_line, QImage.Format_RGB888)
                rgb_pixmap = QPixmap.fromImage(rgb_qimg)

                # Update the display
                self.rgb_display.setPixmap(rgb_pixmap)
                self.rgb_display.setAlignment(Qt.AlignCenter)
                self.rgb_display.setText("")  # Clear any text
            except Exception as e:
                print(f"[GUI] Error updating RGB display: {e}")

        # Update IR display
        if current_ir_frame is not None:
            try:
                # Enhance IR image
                ir_enhanced = normalize_ir(current_ir_frame)

                # Get display dimensions
                ir_display_width = self.ir_display.width()
                ir_display_height = self.ir_display.height()

                # Calculate aspect ratio
                ir_aspect = FRAME_DIMS["IR_WIDTH"] / FRAME_DIMS["IR_HEIGHT"]
                display_aspect = ir_display_width / ir_display_height

                # Calculate dimensions that preserve aspect ratio
                if display_aspect > ir_aspect:
                    # Display is wider than frame
                    target_height = ir_display_height
                    target_width = int(target_height * ir_aspect)
                else:
                    # Display is taller than frame
                    target_width = ir_display_width
                    target_height = int(target_width / ir_aspect)

                # Resize IR frame while preserving aspect ratio
                ir_resized = cv2.resize(ir_enhanced, (target_width, target_height))

                # Convert to QImage and then QPixmap
                ir_qimg = QImage(ir_resized.data, ir_resized.shape[1], ir_resized.shape[0],
                                 ir_resized.shape[1], QImage.Format_Grayscale8)
                ir_pixmap = QPixmap.fromImage(ir_qimg)

                # Update the display
                self.ir_display.setPixmap(ir_pixmap)
                self.ir_display.setAlignment(Qt.AlignCenter)
                self.ir_display.setText("")  # Clear any text
            except Exception as e:
                print(f"[GUI] Error updating IR display: {e}")

        # Handle capture request
        if capture_requested and current_rgb_frame is not None and current_ir_frame is not None:
            global glasses_mode, current_person_folder
            prefix = "glasses" if glasses_mode else "normal"
            self._save_images(current_rgb_frame, current_ir_frame, current_person_folder, prefix)
            capture_requested = False  # Reset flag

            if not glasses_mode:  # Only ask about glasses if we just did a normal capture
                self._ask_glasses_question()
            else:  # If we just captured glasses mode, it's complete
                speak("Capture complete.")
                self.status_bar.showMessage("Capture complete.")

    def on_camera_connected(self, message):
        """Handle camera connection event."""
        self.status_bar.showMessage(message)

    def on_camera_disconnected(self, message):
        """Handle camera disconnection event."""
        self.status_bar.showMessage(message)

    def on_error(self, message):
        """Handle error event."""
        self.status_bar.showMessage(f"Error: {message}")

    def add_new_face(self):
        """Initiates the process to add a new face."""
        global current_person_folder, capture_requested, glasses_mode, current_metadata

        # Check if Kinect client is running
        if not self.check_kinect_client_status():
            return

        name = self.name_input.text().strip()
        identifier = self.id_input.text().strip()
        role = self.selected_role

        if not name or not identifier:
            QMessageBox.critical(self, "Input Error", "Name and ID/Matric Number cannot be empty.")
            return

        # Check if camera is connected and streaming
        if not camera_connected_event.is_set() or current_rgb_frame is None:
            QMessageBox.critical(self, "Camera Error", "Kinect camera is not connected or not streaming.")
            return

        # Show loading indicator
        self.show_loading("Preparing capture...")

        try:
            # Create folder for the person
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Sanitize identifier for folder name by replacing slashes with underscores
            sanitized_identifier = identifier.replace('/', '_')
            folder_name = f"{sanitized_identifier}_{name.replace(' ', '_')}_{timestamp}"
            current_person_folder = os.path.join(SAVE_DIRS[role], folder_name)
            os.makedirs(current_person_folder, exist_ok=True)

            # Prepare metadata - store original identifier with slashes
            current_metadata = {
                "name": name,
                "id": identifier,  # Keep original format with slashes for database
                "role": role,
                "timestamp": timestamp,
                "glasses_mode": self.glasses_checkbox.isChecked() if role == "Student" else False
            }

            # Write metadata to file
            self._write_metadata_to_file(current_person_folder, current_metadata)

            # Hide loading before showing message
            self.hide_loading()

            QMessageBox.information(self, "Capture Ready", f"Get ready for capture: {name} ({role})")
            speak("Please face the camera. Capturing in 3 seconds.")

            glasses_mode = self.glasses_checkbox.isChecked() if role == "Student" else False

            # Start countdown
            self.countdown.move(self.width() // 2 - 100, self.height() // 2 - 100)
            self.countdown.show()
            self.countdown.start_countdown()

        except Exception as e:
            self.hide_loading()
            QMessageBox.critical(self, "Error", f"Failed to prepare capture: {e}")

    def trigger_capture(self):
        """Sets the global flag to request a frame capture."""
        global capture_requested
        capture_requested = True

    def manage_faces(self):
        """Opens the directory where face images are saved based on current role selection."""
        role = self.selected_role
        save_dir = SAVE_DIRS[role]
        os.startfile(save_dir) if sys.platform == 'win32' else os.system(f'xdg-open "{save_dir}"')

    def delete_face(self):
        """Allows user to select and delete a face folder."""
        role = self.selected_role
        save_dir = SAVE_DIRS[role]
        folder = QFileDialog.getExistingDirectory(self, f"Select {role} face folder to delete", save_dir)

        if folder:
            reply = QMessageBox.question(self, "Delete", f"Delete folder: {folder}?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                try:
                    shutil.rmtree(folder)
                    QMessageBox.information(self, "Deleted", f"Deleted {folder}")
                except OSError as e:
                    QMessageBox.critical(self, "Error", f"Could not delete {folder}: {e}")

    def reset_form(self):
        """Resets all input fields in the GUI."""
        self.name_input.clear()
        self.id_input.clear()
        self.student_radio.setChecked(True)
        self.glasses_checkbox.setChecked(False)
        self.on_role_changed()  # Update labels after role reset

    def show_help(self):
        """Shows help information for using the application."""
        help_text = """
                How to Use Kinect Student Capture:

                1. Enter the person's name and ID number
                2. Select their role (Student or Lecturer)
                3. For students, check "Use Glasses Mode" if they wear glasses
                4. Click "Add New Face" to start the capture process
                5. Follow the on-screen instructions

                Tips:
                - Ensure good lighting for best results
                - Look directly at the camera during capture
                - Remove face masks, hats, or other face coverings
                - For students with glasses, two captures will be made

                Keyboard Shortcuts:
                - Ctrl+A: Add New Face
                - Ctrl+M: Manage Faces
                - Ctrl+D: Delete Face
                - Ctrl+R: Reset Form
                - Ctrl+H: Show Help
                - Esc: Exit Application
                """
        QMessageBox.information(self, "Help", help_text)

    def show_about(self):
        """Shows information about the application."""
        about_text = f"""
                {APP_TITLE}

                A facial recognition system for student and lecturer registration
                using Microsoft Kinect for Windows v2.

                Features:
                - RGB and IR camera capture
                - Face detection and recognition
                - Database storage of facial features
                - Support for students with/without glasses

                © 2025 Dosumu Andrew Anchor University Final year Project
                """
        QMessageBox.information(self, "About", about_text)

    def show_loading(self, message):
        """Shows the loading overlay with the specified message."""
        self.loading.set_message(message)
        self.loading.move(self.width() // 2 - 150, self.height() // 2 - 50)
        self.loading.show()
        QApplication.processEvents()  # Force UI update

    def hide_loading(self):
        """Hides the loading overlay."""
        self.loading.hide()
        QApplication.processEvents()  # Force UI update

    def check_kinect_client_status(self):
        """
        Checks if the Kinect client is running and connected.
        Returns True if the client is running and at least one camera is connected.
        """
        global camera_connected_event, connected_cameras, current_rgb_frame

        # First check if the server socket is listening
        if not camera_connected_event.is_set():
            QMessageBox.warning(self, "Server Not Ready",
                                "The server is not ready to accept Kinect connections.\n\n"
                                "Please wait a moment and try again.")
            return False

        # Then check if any cameras are connected
        if not connected_cameras:
            QMessageBox.warning(self, "No Kinect Connected",
                                "No Kinect cameras are currently connected.\n\n"
                                "Please make sure the Kinect client application is running on the computer connected to the Kinect sensor.")
            return False

        return True

    def _save_images(self, rgb_frame, ir_frame, folder_path, prefix):
        """Saves RGB and IR images to the specified folder with the given prefix."""
        try:
            # Create timestamp for filenames
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save RGB image
            rgb_filename = os.path.join(folder_path, f"{prefix}_rgb_{timestamp}.png")
            cv2.imwrite(rgb_filename, rgb_frame)

            # Save IR image
            ir_filename = os.path.join(folder_path, f"{prefix}_ir_{timestamp}.png")

            # Normalize IR for better visibility in saved image
            ir_enhanced = normalize_ir(ir_frame)
            cv2.imwrite(ir_filename, ir_enhanced)

            self.status_bar.showMessage(f"Images saved to {folder_path}")
            return True
        except Exception as e:
            self.status_bar.showMessage(f"Error saving images: {e}")
            return False

    def _write_metadata_to_file(self, folder_path, metadata):
        """Writes metadata to a JSON file in the specified folder."""
        import json
        try:
            metadata_file = os.path.join(folder_path, "metadata.json")
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=4)
            return True
        except Exception as e:
            print(f"Error writing metadata: {e}")
            return False

    def _ask_glasses_question(self):
        """Asks the user if the student wears glasses and initiates a second capture if yes."""
        global glasses_mode, capture_requested

        if QMessageBox.question(self, "Glasses", "Does the person being captured wear glasses?",
                                QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes:
            glasses_mode = True
            speak("Please wear your glasses and face the camera. Capturing in 3 seconds.")

            # Start countdown for glasses capture
            self.countdown.move(self.width() // 2 - 100, self.height() // 2 - 100)
            self.countdown.show()
            self.countdown.start_countdown()
        else:
            glasses_mode = False
            speak("Capture complete.")
            self.status_bar.showMessage("Capture complete.")

    def closeEvent(self, event):
        """Handle window close event."""
        reply = QMessageBox.question(self, 'Exit', 'Are you sure you want to exit?',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            # Stop the receiver thread
            self.receiver_thread.stop()
            self.receiver_thread.wait()

            # Close the server socket
            global server_socket
            if server_socket:
                try:
                    server_socket.close()
                except:
                    pass

            event.accept()
        else:
            event.ignore()


# Main entry point
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set application style
    app.setStyle("Fusion")

    # Create and show the main window
    window = KinectCaptureApp()
    window.show()

    # Start the application event loop
    sys.exit(app.exec_())
