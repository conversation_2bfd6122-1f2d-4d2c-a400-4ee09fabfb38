import sqlite3
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
from datetime import datetime
from db import DB_PATH, get_system_settings

def create_notification(user_id, message, notification_type, related_id=None):
    """Create a new notification for a user"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        
        c.execute("""
            INSERT INTO notifications (user_id, message, type, related_id, timestamp, is_read)
            VALUES (?, ?, ?, ?, ?, 0)
        """, (user_id, message, notification_type, related_id, datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        
        conn.commit()
        return c.lastrowid

def create_absence_notification(student_id, lecture_id):
    """Create an absence notification for a student"""
    # Get student user_id
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # Get student details
        c.execute("SELECT user_id, name, email, phone FROM students WHERE id = ?", (student_id,))
        student = c.fetchone()
        
        if not student:
            return False
        
        # Get lecture details
        c.execute("""
            SELECT l.id, c.code as course_code, c.title as course_name, l.start_time
            FROM lectures l
            JOIN courses c ON l.course_id = c.id
            WHERE l.id = ?
        """, (lecture_id,))
        
        lecture = c.fetchone()
        
        if not lecture:
            return False
        
        # Create in-app notification
        message = f"You were absent from {lecture['course_code']} ({lecture['course_name']}) lecture on {lecture['start_time'].split(' ')[0]}"
        notification_id = create_notification(student['user_id'], message, 'absence', lecture_id)
        
        # Send SMS if phone number is available
        if student['phone']:
            sms_message = f"You missed today's {lecture['course_code']} class — please check in with your lecturer."
            send_sms_notification(student['phone'], sms_message)
        
        # Send email if email is available
        if student['email']:
            email_subject = f"Absence Notification - {lecture['course_code']}"
            email_message = f"""
            Dear {student['name']},
            
            Our records indicate that you were absent from the following lecture:
            
            Course: {lecture['course_code']} - {lecture['course_name']}
            Date: {lecture['start_time'].split(' ')[0]}
            Time: {lecture['start_time'].split(' ')[1]}
            
            Please check in with your lecturer.
            
            Regards,
            Biometric Attendance System
            """
            send_email_notification(student['email'], email_subject, email_message)
        
        return notification_id

def create_late_notification(student_id, lecture_id):
    """Create a late arrival notification for a student"""
    # Get student user_id
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        # Get student details
        c.execute("SELECT user_id FROM students WHERE id = ?", (student_id,))
        student = c.fetchone()
        
        if not student:
            return False
        
        # Get lecture details
        c.execute("""
            SELECT l.id, c.code as course_code, c.name as course_name, l.start_time
            FROM lectures l
            JOIN courses c ON l.course_id = c.id
            WHERE l.id = ?
        """, (lecture_id,))
        
        lecture = c.fetchone()
        
        if not lecture:
            return False
        
        # Create notification
        message = f"You arrived late to {lecture['course_code']} ({lecture['course_name']}) lecture on {lecture['start_time'].split(' ')[0]}"
        
        return create_notification(student['user_id'], message, 'late', lecture_id)

def create_system_notification(user_id, message, related_id=None):
    """Create a system notification for a user"""
    return create_notification(user_id, message, 'system', related_id)

def get_user_notifications(user_id, limit=None, include_read=False):
    """Get notifications for a user"""
    with sqlite3.connect(DB_PATH) as conn:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        
        query = """
            SELECT id, message, type, related_id, timestamp, is_read
            FROM notifications
            WHERE user_id = ?
        """
        
        params = [user_id]
        
        if not include_read:
            query += " AND is_read = 0"
        
        query += " ORDER BY timestamp DESC"
        
        if limit:
            query += " LIMIT ?"
            params.append(limit)
        
        c.execute(query, params)
        
        return [dict(row) for row in c.fetchall()]

def mark_notification_read(notification_id):
    """Mark a notification as read"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        
        c.execute("""
            UPDATE notifications
            SET is_read = 1
            WHERE id = ?
        """, (notification_id,))
        
        conn.commit()
        return c.rowcount > 0

def mark_all_notifications_read(user_id):
    """Mark all notifications for a user as read"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        
        c.execute("""
            UPDATE notifications
            SET is_read = 1
            WHERE user_id = ? AND is_read = 0
        """, (user_id,))
        
        conn.commit()
        return c.rowcount

def delete_notification(notification_id):
    """Delete a notification"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        
        c.execute("""
            DELETE FROM notifications
            WHERE id = ?
        """, (notification_id,))
        
        conn.commit()
        return c.rowcount > 0

def get_unread_notification_count(user_id):
    """Get the count of unread notifications for a user"""
    with sqlite3.connect(DB_PATH) as conn:
        c = conn.cursor()
        
        c.execute("""
            SELECT COUNT(*) FROM notifications
            WHERE user_id = ? AND is_read = 0
        """, (user_id,))
        
        return c.fetchone()[0]

def send_email_notification(to_email, subject, message):
    """Send an email notification"""
    settings = get_system_settings()
    
    if settings.get('enable_email_notifications') != 'true':
        return False
    
    smtp_server = settings.get('email_server')
    smtp_port = int(settings.get('email_port', 587))
    smtp_username = settings.get('email_username')
    smtp_password = settings.get('email_password')
    
    if not all([smtp_server, smtp_port, smtp_username, smtp_password]):
        return False
    
    try:
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = to_email
        msg['Subject'] = subject
        
        msg.attach(MIMEText(message, 'plain'))
        
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()
        
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False

def send_sms_notification(phone_number, message):
    """Send an SMS notification using a third-party service"""
    settings = get_system_settings()
    
    if settings.get('enable_sms_notifications') != 'true':
        return False
    
    sms_api_key = settings.get('sms_api_key')
    sms_api_url = settings.get('sms_api_url')
    
    if not all([sms_api_key, sms_api_url]):
        return False
    
    try:
        payload = {
            'apikey': sms_api_key,
            'to': phone_number,
            'message': message
        }
        
        response = requests.post(sms_api_url, data=payload)
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error sending SMS: {e}")
        return False
