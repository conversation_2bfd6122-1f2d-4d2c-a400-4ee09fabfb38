using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Sockets;
using System.Threading;
using Microsoft.Kinect;
using System.Linq; // Required for .Contains() on args

namespace KinectFaceSender
{
    class Program
    {
        private static KinectSensor _sensor;
        private static MultiSourceFrameReader _reader;
        private static TcpClient _client;
        private static NetworkStream _stream;
        private static bool _isRunning = true;
        private static readonly object _streamLock = new object();
        private static int _frameIntervalMs = 18; // Target ~60fps (1000ms / 60fps = 16.67ms) for attendance mode
        private static DateTime _lastSent = DateTime.MinValue;
        private static int _cameraId = 1; // Default camera ID
        private static int _sequenceNumber = 0; // Frame sequence counter
        private static int _jpegQuality = 70; // Default JPEG quality for attendance mode

        private static bool _verboseLogging = false; // Default to FALSE to reduce console spam

        // New variables for mode control
        private static string _mode = "attendance"; // "capture" or "attendance"

        static void Main(string[] args)
        {
            Console.CancelKeyPress += (s, e) => { _isRunning = false; e.Cancel = true; };

            // Parse command line arguments
            // Check for verbose logging flag first
            //if (args.Contains("--verbose") || args.Contains("-v"))
            //{
            //    _verboseLogging = true;
            //    Console.WriteLine("Verbose logging ENABLED.");
            //}

            // Parse camera ID (first argument if it's a number)
            if (args.Length > 0 && int.TryParse(args[0], out int camId) && camId > 0 && camId <= 10)
            {
                _cameraId = camId;
                Console.WriteLine($"📷 Using camera ID: {_cameraId}");
            }

            // Check for mode argument (second argument, or first if no camera ID)
            string modeArg = args.FirstOrDefault(arg => arg.ToLower() == "attendance" || arg.ToLower() == "capture");
            if (!string.IsNullOrEmpty(modeArg))
            {
                if (modeArg == "attendance")
                {
                    _mode = "attendance";
                    _frameIntervalMs = 18; // Target ~60fps (1000ms / 60fps = 16.67ms)
                    _jpegQuality = 70; // Increased quality with new downscaling
                }
                else if (modeArg == "capture")
                {
                    _mode = "capture";
                    _frameIntervalMs = 100; // Target ~10fps
                    _jpegQuality = 90; // Higher quality for capture app
                }
            }
            else
            {
                Console.WriteLine($"No mode specified. Defaulting to '{_mode}' mode.");
            }

            // Allow custom frame interval override (optional, for fine-tuning)
            // Look for an argument that is purely numeric and not the camera ID
            string customIntervalArg = args.FirstOrDefault(arg => int.TryParse(arg, out int i) && i > 0 && i != _cameraId);
            if (!string.IsNullOrEmpty(customIntervalArg) && int.TryParse(customIntervalArg, out int interval) && interval > 0)
            {
                _frameIntervalMs = interval;
                Console.WriteLine($"⏱️ Custom frame interval: {_frameIntervalMs}ms (~{1000 / _frameIntervalMs} FPS)");
            }


            try
            {
                Console.WriteLine("🔌 Initializing Kinect...");
                _sensor = KinectSensor.GetDefault();
                if (_sensor == null) { Console.WriteLine("❌ No Kinect found."); return; }

                _sensor.Open();
                Console.WriteLine("✅ Kinect initialized.");

                // Attempt to connect to the server with retry logic
                while (_isRunning && (_client == null || !_client.Connected))
                {
                    try
                    {
                        ConnectToServer();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Attempt failed: {ex.Message}. Retrying in 3 seconds...");
                        Thread.Sleep(3000); // Wait 3 seconds before retrying
                    }
                }

                if (_isRunning && _client.Connected)
                {
                    // Open MultiSourceFrameReader for Color, Infrared, and Depth frames
                    _reader = _sensor.OpenMultiSourceFrameReader(FrameSourceTypes.Color | FrameSourceTypes.Infrared | FrameSourceTypes.Depth);
                    _reader.MultiSourceFrameArrived += Reader_MultiSourceFrameArrived;

                    Console.WriteLine("🚀 Streaming started. Press Ctrl+C to exit.");
                    while (_isRunning)
                        Thread.Sleep(100); // Keep the main thread alive
                }
                else
                {
                    Console.WriteLine("🔴 Application stopping because connection could not be established.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
            finally
            {
                Cleanup(); // Ensure resources are released on exit
            }
        }

        private static void ConnectToServer()
        {
            // Clean up any previous client/stream before attempting a new connection
            CleanupClientAndStream();

            Console.WriteLine("📡 Connecting to Python server...");
            _client = new TcpClient();
            _client.ReceiveTimeout = 10000; // 10 second timeout for receives
            _client.SendTimeout = 10000; // 10 second timeout for sends

            // Use larger buffer for sending, especially in high performance mode
            _client.SendBufferSize = _mode == "attendance" ? 4 * 1024 * 1024 : 2 * 1024 * 1024;

            // Always disable Nagle's algorithm for better responsiveness
            _client.NoDelay = true;

            _client.Connect("localhost", 9090); // Connect to the Python server
            _stream = _client.GetStream();
            Console.WriteLine("✅ Connected.");

            // Reset sequence number on new connection
            _sequenceNumber = 0;
        }

        private static void Reader_MultiSourceFrameArrived(object sender, MultiSourceFrameArrivedEventArgs e)
        {
            // If application is stopping or not connected, return
            if (!_isRunning || _stream == null || !_client.Connected) return;

            // Control frame rate based on _frameIntervalMs
            if ((DateTime.Now - _lastSent).TotalMilliseconds < _frameIntervalMs)
                return;

            // Acquire the multi-source frame
            var frame = e.FrameReference.AcquireFrame();
            if (frame == null) return;

            // Use 'using' blocks to ensure frames are disposed properly
            using (var color = frame.ColorFrameReference.AcquireFrame())
            using (var ir = frame.InfraredFrameReference.AcquireFrame())
            using (var depth = frame.DepthFrameReference.AcquireFrame())
            {
                // Ensure all frames are available
                if (color == null || ir == null || depth == null) return;

                try
                {
                    // Convert frames to the format expected by socket_server.py

                    // Pass the mode to the conversion function to handle dynamic scaling
                    byte[] rgbJpeg = ConvertColorFrameToJpeg(color, _jpegQuality, _mode);


                    byte[] irRaw = ConvertUShortFrameToBytes(ir);


                    byte[] depthRaw = ConvertUShortFrameToBytes(depth);

                    if (rgbJpeg == null || irRaw == null || depthRaw == null)
                    {
                        Console.WriteLine("⚠️ Failed to convert one or more frames");
                        return;
                    }

                    // Increment sequence number (1-9999 range to avoid overflow for small int)
                    _sequenceNumber = (_sequenceNumber % 9999) + 1;

                    // Log frame details periodically or verbosely

                    // Use a lock to ensure only one thread sends data at a time
                    lock (_streamLock)
                    {
                        try
                        {
                            // Create header (20 bytes: seq, rgb_len, ir_len, depth_len, camera_id)
                            byte[] header = new byte[20];

                            // Fill header with sequence number and frame sizes (little-endian)
                            BitConverter.GetBytes(_sequenceNumber).CopyTo(header, 0);
                            BitConverter.GetBytes(rgbJpeg.Length).CopyTo(header, 4);
                            BitConverter.GetBytes(irRaw.Length).CopyTo(header, 8);
                            BitConverter.GetBytes(depthRaw.Length).CopyTo(header, 12);
                            BitConverter.GetBytes(_cameraId).CopyTo(header, 16);

                            // Debug output to verify header values if verbose logging is on
                            if (_verboseLogging)
                            {
                                Console.WriteLine($"Header values: seq={_sequenceNumber}, rgb={rgbJpeg.Length}, ir={irRaw.Length}, depth={depthRaw.Length}, cam={_cameraId}");
                                Console.WriteLine($"Header bytes: {BitConverter.ToString(header)}");
                            }

                            // Send header first

                            _stream.Write(header, 0, header.Length);
                            _stream.Flush(); // Ensure header is sent immediately

                            // Send RGB frame data

                            _stream.Write(rgbJpeg, 0, rgbJpeg.Length);
                            _stream.Flush();

                            // Send IR frame data

                            _stream.Write(irRaw, 0, irRaw.Length);
                            _stream.Flush();

                            // Send Depth frame data

                            _stream.Write(depthRaw, 0, depthRaw.Length);
                            _stream.Flush();

                            // Wait for acknowledgment from the server

                            byte[] ack = new byte[1];
                            int bytesRead = _stream.Read(ack, 0, 1); // Read 1 byte ACK

                            if (bytesRead == 1 && ack[0] == 0x01)
                            {
                                _lastSent = DateTime.Now; // Update last sent time only on successful ACK
                                // Only log acknowledgment if verbose logging is enabled
                                if (_verboseLogging)
                                {
                                    Console.WriteLine($"✅ Frame #{_sequenceNumber} acknowledged");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"⚠️ Frame not acknowledged properly. Received: {(bytesRead > 0 ? ack[0].ToString() : "nothing")}");
                                // If ACK fails, consider the connection broken and try to reconnect
                                throw new IOException("Server did not acknowledge frame.");
                            }
                        }
                        catch (IOException ex)
                        {
                            // Handle network errors (e.g., server disconnected)
                            Console.WriteLine($"⚠️ Network error during transmission: {ex.Message}");
                            // Cleanup resources and flag for reconnection attempt in the main loop
                            CleanupClientAndStream();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ Unexpected error during frame transmission: {ex.Message}");
                            Console.WriteLine(ex.StackTrace); // Print stack trace for debugging
                            // If any other error, also consider the connection broken
                            CleanupClientAndStream();
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ Error processing frame: {ex.Message}");
                    Console.WriteLine(ex.StackTrace);
                }
            }
        }

        // Modified to accept a 'mode' parameter for dynamic scaling
        private static byte[] ConvertColorFrameToJpeg(ColorFrame colorFrame, int quality, string mode)
        {
            try
            {
                int originalWidth = colorFrame.FrameDescription.Width;
                int originalHeight = colorFrame.FrameDescription.Height;

                int targetWidth = originalWidth;
                int targetHeight = originalHeight;

                // Apply downscaling for "attendance" mode to reduce data size for higher FPS
                if (mode == "attendance")
                {
                    // Downscale 1920x1080 to 960x540 (half resolution) for attendance mode
                    targetWidth = originalWidth / 2; // 1920 / 2 = 960
                    targetHeight = originalHeight / 2; // 1080 / 2 = 540
                    if (_verboseLogging) // Only log this if verbose logging is on
                    {
                        Console.WriteLine($"Downscaling RGB frame to {targetWidth}x{targetHeight} for attendance mode.");
                    }
                }
                // For "capture" mode, targetWidth and targetHeight remain originalWidth/Height, ensuring 1080p.

                // Buffer to copy color frame data (BGRA format)
                byte[] colorBuffer = new byte[originalWidth * originalHeight * 4];
                colorFrame.CopyConvertedFrameDataToArray(colorBuffer, ColorImageFormat.Bgra);

                using (Bitmap originalBitmap = new Bitmap(originalWidth, originalHeight, System.Drawing.Imaging.PixelFormat.Format32bppArgb))
                {
                    // Lock bitmap data for direct memory access
                    BitmapData bmpData = originalBitmap.LockBits(
                        new Rectangle(0, 0, originalWidth, originalHeight),
                        ImageLockMode.WriteOnly,
                        originalBitmap.PixelFormat);

                    try
                    {
                        IntPtr ptr = bmpData.Scan0;
                        // Copy BGRA bytes to bitmap's memory
                        System.Runtime.InteropServices.Marshal.Copy(colorBuffer, 0, ptr, colorBuffer.Length);
                    }
                    finally
                    {
                        originalBitmap.UnlockBits(bmpData); // Unlock bitmap data
                    }

                    Bitmap finalBitmap = originalBitmap;
                    // If downscaling is needed, create a new bitmap and draw the scaled image
                    if (mode == "attendance")
                    {
                        finalBitmap = new Bitmap(targetWidth, targetHeight);
                        using (Graphics g = Graphics.FromImage(finalBitmap))
                        {
                            // Use high quality interpolation for better downscaling results
                            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                            g.DrawImage(originalBitmap, 0, 0, targetWidth, targetHeight);
                        }
                    }

                    // Get JPEG encoder
                    ImageCodecInfo jpegEncoder = GetEncoder(ImageFormat.Jpeg);
                    EncoderParameters encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, quality); // Set JPEG quality

                    using (MemoryStream ms = new MemoryStream())
                    {
                        // Save the (potentially scaled) bitmap to a memory stream as JPEG
                        finalBitmap.Save(ms, jpegEncoder, encoderParams);
                        byte[] jpegData = ms.ToArray();
                        if (_verboseLogging) Console.WriteLine($"JPEG conversion successful. Size: {jpegData.Length} bytes");
                        return jpegData;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error converting color frame to JPEG: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                return null;
            }
        }

        // Helper to get the JPEG encoder
        private static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }

        // Converts ushort frames (IR, Depth) to byte array
        private static byte[] ConvertUShortFrameToBytes(dynamic frame)
        {
            try
            {
                ushort[] buffer = new ushort[frame.FrameDescription.LengthInPixels];
                frame.CopyFrameDataToArray(buffer); // Copy ushort data
                byte[] bytes = new byte[buffer.Length * 2]; // Each ushort is 2 bytes
                Buffer.BlockCopy(buffer, 0, bytes, 0, bytes.Length); // Efficiently copy to byte array
                return bytes;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error converting frame: {ex.Message}");
                return null;
            }
        }

        // Cleans up only client and stream, allowing for reconnection
        private static void CleanupClientAndStream()
        {
            if (_stream != null)
            {
                try { _stream.Close(); } catch (Exception ex) { Console.WriteLine($"Error closing stream: {ex.Message}"); }
                _stream = null;
            }
            if (_client != null)
            {
                try { _client.Close(); } catch (Exception ex) { Console.WriteLine($"Error closing client: {ex.Message}"); }
                _client = null;
            }
        }

        // Cleans up all Kinect and network resources for application shutdown
        private static void Cleanup()
        {
            Console.WriteLine("🧹 Cleaning up resources...");
            _reader?.Dispose(); // Dispose frame reader
            _sensor?.Close(); // Close Kinect sensor
            CleanupClientAndStream(); // Use the dedicated function for client/stream cleanup
            Console.WriteLine("✅ Shutdown complete.");
        }
    }
}