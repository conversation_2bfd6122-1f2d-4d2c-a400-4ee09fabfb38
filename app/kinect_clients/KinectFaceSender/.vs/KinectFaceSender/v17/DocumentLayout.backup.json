{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\PyCharm projects\\app\\kinect_clients\\KinectFaceSender\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{06CD5EF4-5694-4FF2-8BA4-F0CC6F3C36CE}|KinectFaceSender.csproj|c:\\users\\<USER>\\documents\\pycharm projects\\app\\kinect_clients\\kinectfacesender\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06CD5EF4-5694-4FF2-8BA4-F0CC6F3C36CE}|KinectFaceSender.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{00000000-0000-0000-0000-000000000000}|<Solution>|KinectFaceSender||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "KinectFaceSender", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\PyCharm projects\\app\\kinect_clients\\KinectFaceSender\\KinectFaceSender.csproj", "RelativeDocumentMoniker": "KinectFaceSender.csproj", "ToolTip": "C:\\Users\\<USER>\\Documents\\PyCharm projects\\app\\kinect_clients\\KinectFaceSender\\KinectFaceSender.csproj", "RelativeToolTip": "KinectFaceSender.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-05-16T21:18:32.85Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\PyCharm projects\\app\\kinect_clients\\KinectFaceSender\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\PyCharm projects\\app\\kinect_clients\\KinectFaceSender\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-15T09:10:47.094Z", "EditorCaption": ""}]}]}]}