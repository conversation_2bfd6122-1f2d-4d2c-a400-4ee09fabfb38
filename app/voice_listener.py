import whisper
import pyaudio
import wave
import os
import time
import face_recognition
import cv2
import numpy as np
from recognizer import load_lecturer_faces
from playsound import playsound
import threading

# Import the audio buffer from socket_server
try:
    from socket_server import audio_buffer, get_audio_chunk
    USING_KINECT_AUDIO = True
    print("✅ Using Kinect audio from socket server")
except ImportError:
    USING_KINECT_AUDIO = False
    print("⚠️ Kinect audio not available, using system microphone")

# Load lecturer faces when the module is imported
lecturer_encodings, lecturer_names = load_lecturer_faces()

# Whisper model
model = whisper.load_model("base")

# Audio config
RECORD_SECONDS = 5
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
TEMP_FILENAME = "temp_audio.wav"
WAKE_WORDS = ["hello mikas", "hi mikas", "hey mikas"]
BEEP_SOUND = "./assets/alert.mp3"  # Path to your beep sound file

def record_audio_from_kinect(filename, duration=RECORD_SECONDS):
    """Record audio from Kinect via socket_server's audio buffer"""
    if not USING_KINECT_AUDIO:
        return None
        
    frames = []
    end_time = time.time() + duration
    
    print("🎙️ Recording from Kinect...")
    while time.time() < end_time:
        chunk = get_audio_chunk(timeout=0.1)
        if chunk:
            frames.append(chunk)
    
    if not frames:
        print("⚠️ No audio received from Kinect")
        return None
    
    # Save frames to WAV file
    wf = wave.open(filename, 'wb')
    wf.setnchannels(CHANNELS)
    wf.setsampwidth(2)  # Assuming 16-bit audio
    wf.setframerate(RATE)
    wf.writeframes(b''.join(frames))
    wf.close()
    
    return filename

def record_audio_from_system(filename, duration=RECORD_SECONDS):
    """Record audio from system microphone (fallback)"""
    audio = pyaudio.PyAudio()
    stream = audio.open(format=FORMAT, channels=CHANNELS,
                        rate=RATE, input=True,
                        frames_per_buffer=CHUNK)
    print("🎙️ Recording from system mic...")
    frames = []

    for _ in range(0, int(RATE / CHUNK * duration)):
        data = stream.read(CHUNK)
        frames.append(data)

    stream.stop_stream()
    stream.close()
    audio.terminate()

    wf = wave.open(filename, 'wb')
    wf.setnchannels(CHANNELS)
    wf.setsampwidth(audio.get_sample_size(FORMAT))
    wf.setframerate(RATE)
    wf.writeframes(b''.join(frames))
    wf.close()
    
    return filename

def record_audio(filename, duration=RECORD_SECONDS):
    """Record audio, trying Kinect first then falling back to system mic"""
    # Try to record from Kinect
    if USING_KINECT_AUDIO:
        result = record_audio_from_kinect(filename, duration)
        if result:
            return result
    
    # Fallback to system microphone
    return record_audio_from_system(filename, duration)

def transcribe_audio(filename):
    try:
        result = model.transcribe(filename)
        return result["text"].strip().lower()
    except Exception as e:
        print(f"Transcription error: {e}")
        return ""

def play_beep():
    """Play beep sound when wake word is detected"""
    try:
        if os.path.exists(BEEP_SOUND):
            playsound(BEEP_SOUND)
        else:
            print(f"⚠️ Beep sound file not found: {BEEP_SOUND}")
    except Exception as e:
        print(f"❌ Error playing beep sound: {e}")

def listen_for_wake_word():
    print("🔊 Passive listening for wake words...")
    while True:
        record_audio(TEMP_FILENAME, duration=3)  # Shorter duration for wake word detection
        text = transcribe_audio(TEMP_FILENAME)
        print(f"🗣️ Detected: {text}")

        # Check if any wake word is in the transcribed text
        if any(wake_word in text for wake_word in WAKE_WORDS):
            print("✅ Wake word recognized!")
            play_beep()  # Play beep sound
            handle_attendance_flow()

        os.remove(TEMP_FILENAME)

def listen_for_response(prompt, duration=RECORD_SECONDS):
    print(prompt)
    time.sleep(1)  # small delay
    record_audio(TEMP_FILENAME, duration)
    text = transcribe_audio(TEMP_FILENAME)
    os.remove(TEMP_FILENAME)
    return text

def handle_attendance_flow():
    # 👤 Use facial recognition to identify the lecturer
    lecturer_name, staff_id = identify_lecturer_face()

    if not lecturer_name or not staff_id:
        print("🚫 Could not identify lecturer. Please stand in front of the camera.")
        speak("Could not identify lecturer. Please try again.")
        return

    print(f"🤖 Hello there, Lecturer {lecturer_name.capitalize()}!")
    speak(f"Hello {lecturer_name}. What course are you taking today?")

    # Get lecturer's assigned courses from database
    from db import get_lecturer_by_staff_id, get_lecturer_courses
    
    lecturer = get_lecturer_by_staff_id(staff_id)
    if not lecturer:
        print(f"❌ Lecturer with staff ID {staff_id} not found in database")
        speak("Sorry, I couldn't find your profile in the system. Please contact the administrator.")
        return
        
    assigned_courses = get_lecturer_courses(lecturer['id'])
    if not assigned_courses:
        print(f"⚠️ Lecturer {lecturer_name} has no assigned courses")
        speak("You don't have any assigned courses. Please contact the administrator.")
        return
        
    # Print available courses for reference
    print("📚 Your assigned courses:")
    for course in assigned_courses:
        print(f"  - {course['code']}: {course['title']}")

    # Ask for course code
    course_code = listen_for_response("📚 What course are you taking today?")
    print(f"✅ Course detected: {course_code.upper()}")
    
    # Validate course code against assigned courses
    course_code = course_code.upper().replace(" ", "")
    valid_course = None
    
    for course in assigned_courses:
        if course_code in course['code'].upper().replace(" ", ""):
            valid_course = course
            break
    
    if not valid_course:
        print(f"❌ Course {course_code} not found in lecturer's assigned courses")
        speak(f"Sorry, {course_code} is not in your assigned courses. Please try again.")
        return
    
    # Use the validated course
    course_code = valid_course['code']
    print(f"✅ Using course: {course_code} - {valid_course['title']}")

    # Ask for duration
    duration_text = listen_for_response("⏱️ How long is the lecture in minutes?")
    try:
        duration = int(''.join(filter(str.isdigit, duration_text)))
    except:
        duration = 60  # default fallback
        print("⚠️ Couldn't detect time. Defaulting to 60 minutes.")

    print(f"🎬 Starting attendance for {course_code} for {duration} minutes...")
    speak(f"Starting attendance for {course_code} for {duration} minutes.")

    # Start the lecture session
    from db import create_lecture
    lecture_id = create_lecture(valid_course['id'], lecturer['id'], duration)
    
    if lecture_id:
        print(f"✅ Lecture session created with ID: {lecture_id}")
        # You could start the attendance system here
        # call_attendance(lecture_id)
    else:
        print("❌ Failed to create lecture session")
        speak("Sorry, there was an error starting the lecture. Please try again.")

def speak(text):
    """Text-to-speech function"""
    try:
        import pyttsx3
        engine = pyttsx3.init()
        engine.say(text)
        engine.runAndWait()
    except Exception as e:
        print(f"❌ Text-to-speech error: {e}")

def identify_lecturer_face():
    """
    Identifies a lecturer using facial recognition.
    Returns the lecturer's name and staff ID if found, None otherwise.
    """
    global lecturer_encodings, lecturer_names
    
    # If no lecturer faces are loaded, try loading them again
    if not lecturer_encodings:
        lecturer_encodings, lecturer_names = load_lecturer_faces()
        if not lecturer_encodings:
            print("❌ No lecturer faces available for recognition")
            return None, None
    
    try:
        # Initialize camera
        cap = cv2.VideoCapture(0)
        if not cap.isOpened():
            print("❌ Could not open camera")
            return None, None
            
        print("🎥 Looking for lecturer face...")
        # Capture a few frames to allow camera to adjust
        for _ in range(10):
            ret, frame = cap.read()
            time.sleep(0.1)
            
        # Capture frame for face recognition
        ret, frame = cap.read()
        if not ret:
            print("❌ Failed to capture frame")
            cap.release()
            return None, None
            
        # Convert to RGB (face_recognition uses RGB)
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Detect faces
        face_locations = face_recognition.face_locations(rgb_frame)
        if not face_locations:
            print("❌ No face detected")
            cap.release()
            return None, None
            
        # Get face encoding
        face_encoding = face_recognition.face_encodings(rgb_frame, face_locations)[0]
        
        # Release camera
        cap.release()
        
        # Compare with known lecturer faces
        if lecturer_encodings:
            face_distances = face_recognition.face_distance(lecturer_encodings, face_encoding)
            best_match_index = np.argmin(face_distances)
            
            if face_distances[best_match_index] < 0.6:  # Threshold for a good match
                lecturer_name = lecturer_names[best_match_index]
                print(f"✅ Identified lecturer: {lecturer_name}")
                return lecturer_name, lecturer_names[best_match_index]
        
        print("❌ Unknown lecturer")
        return None, None
            
    except Exception as e:
        print(f"❌ Error in lecturer identification: {e}")
        return None, None

if __name__ == "__main__":
    listen_for_wake_word()
