import numpy as np
import cv2
from datetime import datetime, timedelta
import pyttsx3
from collections import deque
from config import (
    depth_range_mm,
    ir_reflectance_threshold,
    micro_movement_jitter_px,
    micro_movement_window_sec
)
from logger import log_spoofing_attempt, log_system_event

# Face position tracker
_face_position_history = {}

# Text-to-speech engine (singleton)
_engine = pyttsx3.init()
_engine.setProperty('rate', 180)

# Load mask detection model once
_mask_model = None
try:
    # Try to import TensorFlow with a compatible protobuf
    import os
    os.environ['PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION'] = 'python'
    from tensorflow.keras.models import load_model
    _mask_model = load_model("mask_detector.model")
    print("Mask model loaded successfully")
except Exception as e:
    print(f"Warning: Mask detection model could not be loaded: {e}")
    print("System will use fallback mask detection method")
    _mask_model = None

def speak(text):
    try:
        _engine.say(text)
        _engine.runAndWait()
    except:
        pass  # Avoid crashing if sound fails


def is_live_face(name, depth_frame, ir_frame, bbox, camera_id=0):
    """
    Perform 3-step liveness detection:
    1. 3D depth check
    2. IR intensity check
    3. Passive micro head movement check
    """
    top, right, bottom, left = bbox

    # ✅ 1. Depth check
    face_depth = depth_frame[top:bottom, left:right]
    if face_depth.size == 0:
        return False

    # Filter out zero values (invalid depth readings)
    valid_depths = face_depth[face_depth > 0]
    if valid_depths.size == 0:
        log_system_event(f"❌ Rejected: No valid depth readings for {name}", "WARNING")
        return False

    mean_depth = np.mean(valid_depths)
    if not (depth_range_mm[0] < mean_depth < depth_range_mm[1]):
        log_spoofing_attempt(camera_id, f"Invalid depth {mean_depth:.1f}mm for {name}")
        log_system_event(f"❌ Rejected: Invalid depth {mean_depth:.1f} mm for {name}", "WARNING")
        return False

    # ✅ 2. IR reflectance check
    face_ir = ir_frame[top:bottom, left:right]
    if face_ir.size == 0:
        return False

    mean_ir = np.mean(face_ir)
    if mean_ir < ir_reflectance_threshold:
        print(f"❌ Rejected: Low IR {mean_ir:.1f}")
        return False

    # ✅ 3. Passive motion using standard deviation
    now = datetime.now()
    center = ((top + bottom) // 2, (left + right) // 2)

    if name not in _face_position_history:
        _face_position_history[name] = deque(maxlen=20)  # Store limited history
    _face_position_history[name].append((now, center))

    # Filter out old positions
    _face_position_history[name] = deque(
        [(t, pos) for (t, pos) in _face_position_history[name]
         if now - t <= timedelta(seconds=micro_movement_window_sec)],
        maxlen=20
    )

    positions = [pos for (t, pos) in _face_position_history[name]]
    if len(positions) >= 2:
        xs = [pos[0] for pos in positions]
        ys = [pos[1] for pos in positions]
        std_dev = np.mean([np.std(xs), np.std(ys)])

        if std_dev < micro_movement_jitter_px:
            print(f"❌ Rejected: Low jitter std deviation ({std_dev:.2f}px)")
            return False

    return True


def is_masked_face(face_img):
    """
    Uses a CNN model to detect if the person is wearing a face mask.
    Returns True if a mask is detected.
    """
    # If model failed to load, use a fallback approach
    if _mask_model is None:
        return fallback_mask_detection(face_img)
        
    try:
        # Resize to match the expected input size
        face_resized = cv2.resize(face_img, (128, 128))  # Changed to 128x128
        face_array = face_resized.astype("float") / 255.0
        face_array = np.expand_dims(face_array, axis=0)

        # Get prediction - returns [mask_prob, without_mask_prob]
        prediction = _mask_model.predict(face_array, verbose=0)[0]
        
        # Check if we have two values (mask, withoutMask)
        if len(prediction) == 2:
            mask_prob, without_mask_prob = prediction
            is_masked = mask_prob > without_mask_prob
        else:
            # Fallback to original approach if model output is different
            is_masked = prediction[0] > 0.5

        if is_masked:
            print("😷 Mask detected (ML model)")
            speak("Please remove all face coverings or your attendance won't be marked. Thank you.")
            return True
        else:
            return False
    except Exception as e:
        print(f"Mask detection failed: {e}")
        return fallback_mask_detection(face_img)


def fallback_mask_detection(face_img):
    """
    Fallback method for mask detection using OpenCV features.
    """
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(face_img, cv2.COLOR_BGR2GRAY)
        
        # Use Haar cascade for face detection
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
        
        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) == 0:
            return False
            
        # For each face, check if mouth is visible
        for (x, y, w, h) in faces:
            # Define ROI for mouth (lower half of face)
            roi_gray = gray[y + int(h/2):y + h, x:x + w]
            
            # Detect mouth in the lower half of face
            mouths = mouth_cascade.detectMultiScale(roi_gray, 1.5, 20)
            
            # If no mouth detected, likely masked
            if len(mouths) == 0:
                print("😷 Mask detected (landmark method)")
                speak("Please remove all face coverings or your attendance won't be marked. Thank you.")
                return True
                
        return False
        
    except Exception as e:
        print(f"Fallback mask detection failed: {e}")
        return False


def check_eye_blink(face_img):
    """
    Detect eye blinks as an additional liveness check.
    Returns True if eyes are detected and at least one blink is observed.
    """
    try:
        import dlib
        
        # Initialize face detector and landmark predictor
        detector = dlib.get_frontal_face_detector()
        predictor = dlib.shape_predictor("shape_predictor_68_face_landmarks.dat")
        
        # Convert to grayscale
        gray = cv2.cvtColor(face_img, cv2.COLOR_BGR2GRAY)
        
        # Detect faces
        faces = detector(gray)
        if not faces:
            return False
            
        # Get facial landmarks
        landmarks = predictor(gray, faces[0])
        
        # Calculate eye aspect ratio (EAR)
        def eye_aspect_ratio(eye):
            # Compute the euclidean distances between the two sets of
            # vertical eye landmarks (x, y)-coordinates
            A = np.linalg.norm(eye[1] - eye[5])
            B = np.linalg.norm(eye[2] - eye[4])
            
            # Compute the euclidean distance between the horizontal
            # eye landmark (x, y)-coordinates
            C = np.linalg.norm(eye[0] - eye[3])
            
            # Compute the eye aspect ratio
            ear = (A + B) / (2.0 * C)
            return ear
            
        # Extract eye landmarks
        left_eye = np.array([(landmarks.part(i).x, landmarks.part(i).y) for i in range(36, 42)])
        right_eye = np.array([(landmarks.part(i).x, landmarks.part(i).y) for i in range(42, 48)])
        
        # Calculate EAR for both eyes
        left_ear = eye_aspect_ratio(left_eye)
        right_ear = eye_aspect_ratio(right_eye)
        
        # Average the EAR
        ear = (left_ear + right_ear) / 2.0
        
        # Check if eyes are closed (EAR < threshold)
        return ear < 0.2  # Threshold for closed eyes
        
    except Exception as e:
        print(f"Eye blink detection failed: {e}")
        return True  # Default to True to not block attendance if this check fails



def prune_face_history(max_age_seconds=60):
    """Clean up stale face tracking data"""
    now = datetime.now()
    to_delete = []

    for name, history in _face_position_history.items():
        if not history:
            to_delete.append(name)
            continue
        latest_time = history[-1][0]
        if (now - latest_time).total_seconds() > max_age_seconds:
            to_delete.append(name)

    for name in to_delete:
        del _face_position_history[name]
