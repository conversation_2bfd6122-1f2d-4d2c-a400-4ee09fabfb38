# Production Control Center - User Guide

## Overview
The Production Control Center is a comprehensive GUI application that provides centralized management for all components of the Biometric Attendance System. It eliminates the need to switch between multiple applications and provides real-time monitoring, control, and configuration capabilities.

## Features

### 🎛️ **Centralized Component Control**
- **Kinect Client Management**: Start/stop the Visual Studio Kinect client
- **Socket Server Control**: Manage the Python socket server
- **Web Portal Management**: Control the Flask web application
- **Student Capture App**: Launch the face capture application
- **One-Click Operations**: Start/stop/restart all components simultaneously

### 📊 **Real-Time System Monitoring**
- **Live Status Cards**: Visual indicators for each component
- **System Health Metrics**: CPU, memory, and disk usage monitoring
- **Process Tracking**: Monitor running processes with PID information
- **Connection Status**: Real Kinect connection detection (not just port checking)

### 📋 **Advanced Log Management**
- **Real-Time Log Viewer**: Live system logs with auto-refresh
- **Advanced Filtering**: Filter by log level (INFO, WARNING, ERROR)
- **Search Functionality**: Search through log messages
- **Export Capabilities**: Export logs to CSV format
- **Log Cleanup**: Clear old logs with confirmation

### ⚙️ **Live System Settings**
- **Dynamic Configuration**: Update settings without restarting components
- **Face Recognition Confidence**: Adjust recognition threshold in real-time
- **Late Arrival Threshold**: Configure grace period for attendance
- **Depth Range Settings**: Set valid depth range for liveness detection
- **IR Threshold**: Configure infrared reflectance threshold
- **Instant Apply**: Settings take effect immediately

### 👥 **Face Management System**
- **Automated Import**: Import face images from the images folder
- **Database Integration**: Sync face encodings with the database
- **Status Monitoring**: Track face images and database status
- **Scan Functionality**: Detect new face images automatically

## Quick Start

### Method 1: Using the Batch File (Recommended)
1. Double-click `start_control_center.bat`
2. The script will automatically check dependencies and start the application

### Method 2: Manual Start
1. Open Command Prompt or PowerShell
2. Navigate to the app directory
3. Run: `python production_control_center.py`

## System Requirements

### Software Requirements
- **Python 3.7+** with the following packages:
  - PyQt5
  - psutil
  - sqlite3 (built-in)
- **Visual Studio** (for Kinect client compilation)
- **Kinect SDK 2.0** (for Kinect functionality)

### Hardware Requirements
- **Kinect v2 Sensor** (for biometric capture)
- **Minimum 4GB RAM** (8GB recommended)
- **2GB free disk space**
- **USB 3.0 port** (for Kinect connection)

## User Interface Guide

### 1. System Overview Tab
- **Status Cards**: Green = Running, Red = Stopped, Gray = Checking
- **System Health**: Real-time CPU, memory, and disk usage
- **Recent Activity**: Latest system events and notifications

### 2. Component Control Tab
- **Individual Controls**: Start/stop each component separately
- **Quick Actions**: Bulk operations for all components
- **Process Monitor**: View running processes and their status
- **Status Labels**: Real-time status updates for each component

### 3. System Logs Tab
- **Filter Controls**: Filter by log level and search text
- **Auto Refresh**: Toggle automatic log updates
- **Export Function**: Save logs to CSV file
- **Clear Logs**: Remove old log entries

### 4. Live Settings Tab
- **Confidence Slider**: Adjust face recognition sensitivity
- **Late Threshold**: Set grace period for late arrivals
- **Depth Range**: Configure valid depth for liveness detection
- **IR Threshold**: Set infrared reflectance minimum
- **Apply Button**: Save and activate all settings

### 5. Face Management Tab
- **Import Button**: Process face images from folder
- **Scan Function**: Check for new face images
- **Database Status**: View face encoding statistics
- **Progress Tracking**: Monitor import operations

## Troubleshooting

### Common Issues

#### "Kinect shows as disconnected"
- Ensure Kinect v2 is properly connected via USB 3.0
- Check that Kinect SDK 2.0 is installed
- Verify the KinectFaceSender.exe is compiled and accessible
- Try restarting the Kinect client from the Control Center

#### "Socket server won't start"
- Check if port 9090 is already in use
- Ensure no firewall is blocking the port
- Verify Python has necessary permissions
- Try restarting all components

#### "Web portal not accessible"
- Check if port 5000 is available
- Ensure Flask dependencies are installed
- Verify database connectivity
- Try opening http://localhost:5000 manually

#### "Face import fails"
- Ensure images/student_faces directory exists
- Check that face images are in supported formats (JPG, PNG)
- Verify database is accessible and writable
- Check system logs for detailed error messages

### Performance Optimization

#### For Low-End Systems
- Reduce monitoring frequency in settings
- Disable auto-refresh for logs
- Close unnecessary applications
- Consider reducing Kinect frame rate

#### For High-End Systems
- Enable all monitoring features
- Use real-time log updates
- Run multiple components simultaneously
- Enable advanced liveness detection features

## Security Considerations

### Production Deployment
- Change default passwords in the web portal
- Enable HTTPS for web portal (set secure_cookies = True in config)
- Restrict network access to necessary ports only
- Regular database backups
- Monitor system logs for suspicious activity

### Data Protection
- Face encodings are stored securely in the database
- Personal information is encrypted
- Access logs are maintained
- Regular security updates recommended

## Maintenance

### Regular Tasks
- **Daily**: Check system status and logs
- **Weekly**: Backup database using the built-in function
- **Monthly**: Clear old logs and update face encodings
- **Quarterly**: Review and update system settings

### Backup Procedures
1. Use the "Backup Database" function in the Tools menu
2. Export system logs regularly
3. Keep copies of face images in a separate location
4. Document any custom configuration changes

## Support and Updates

### Getting Help
- Check the system logs for error details
- Use the export function to share logs with support
- Refer to the main project documentation
- Contact the development team with specific issues

### Version Information
- **Current Version**: 1.0.0
- **Last Updated**: June 2025
- **Compatibility**: Python 3.7+, PyQt5, Kinect SDK 2.0

## Advanced Features

### API Integration
The Control Center can be extended with additional API endpoints for:
- Remote monitoring
- Automated deployment
- Integration with other systems
- Custom reporting

### Customization
- Modify themes and colors in the source code
- Add custom monitoring metrics
- Extend logging capabilities
- Create custom export formats

---

**Note**: This Control Center is designed for production use and provides enterprise-level management capabilities for the Biometric Attendance System. For development and testing, individual components can still be run separately.
