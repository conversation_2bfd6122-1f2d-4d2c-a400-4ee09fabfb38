import logging
import os
from datetime import datetime
import sys
import re

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging format
LOG_FORMAT = '%(asctime)s [%(levelname)s] %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Create a logger
logger = logging.getLogger('biometric_system')
logger.setLevel(logging.INFO)

# Create file handler for all logs
log_filename = f"logs/system_{datetime.now().strftime('%Y%m%d')}.log"
file_handler = logging.FileHandler(log_filename, encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

# Create console handler with emoji stripping for Windows
class EmojiStrippingStreamHandler(logging.StreamHandler):
    def emit(self, record):
        try:
            msg = self.format(record)
            # Strip emoji characters for Windows console
            if sys.platform == 'win32':
                # This regex pattern matches emoji characters
                emoji_pattern = re.compile("["
                                           u"\U0001F600-\U0001F64F"  # emoticons
                                           u"\U0001F300-\U0001F5FF"  # symbols & pictographs
                                           u"\U0001F680-\U0001F6FF"  # transport & map symbols
                                           u"\U0001F700-\U0001F77F"  # alchemical symbols
                                           u"\U0001F780-\U0001F7FF"  # Geometric Shapes
                                           u"\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
                                           u"\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
                                           u"\U0001FA00-\U0001FA6F"  # Chess Symbols
                                           u"\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
                                           u"\*********-\U000027B0"  # Dingbats
                                           u"\U000024C2-\U0000257F"  # Enclosed characters
                                           "]+", flags=re.UNICODE)
                msg = emoji_pattern.sub('', msg)
            stream = self.stream
            stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

console_handler = EmojiStrippingStreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))

# Add handlers to logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Create separate attendance logger
attendance_logger = logging.getLogger('attendance')
attendance_logger.setLevel(logging.INFO)

attendance_log = f"logs/attendance_{datetime.now().strftime('%Y%m%d')}.log"
attendance_handler = logging.FileHandler(attendance_log, encoding='utf-8')
attendance_handler.setFormatter(logging.Formatter('%(asctime)s,%(message)s', DATE_FORMAT))
attendance_logger.addHandler(attendance_handler)

# Create separate security logger for spoofing attempts
security_logger = logging.getLogger('security')
security_logger.setLevel(logging.INFO)

security_log = f"logs/security_{datetime.now().strftime('%Y%m%d')}.log"
security_handler = logging.FileHandler(security_log, encoding='utf-8')
security_handler.setFormatter(logging.Formatter(LOG_FORMAT, DATE_FORMAT))
security_logger.addHandler(security_handler)

# Convenience functions
def log_entry(student_name, camera_id):
    attendance_logger.info(f"ENTRY,{student_name},{camera_id}")
    
def log_exit(student_name, camera_id, duration_minutes):
    attendance_logger.info(f"EXIT,{student_name},{camera_id},{duration_minutes}")
    
def log_spoofing_attempt(camera_id, details):
    security_logger.warning(f"SPOOFING_ATTEMPT,{camera_id},{details}")
    
def log_system_event(message, level="INFO"):
    if level == "INFO":
        logger.info(message)
    elif level == "WARNING":
        logger.warning(message)
    elif level == "ERROR":
        logger.error(message)
    elif level == "CRITICAL":
        logger.critical(message)