import socket
import threading
import cv2
import numpy as np
import queue
import time
import sys
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, Q<PERSON>abel, QVBoxLayout, QHBoxLayout, QWidget, QGroupBox
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread

from attendance import (
    process_frame,
    handle_exit_check,
    reset_seen_now,
    finalize_attendance,
    save_attendance,
    LECTURE_DURATION_MINUTES,
    check_lecture_end,
)
from logger import log_system_event
import sys
from db import get_lecture_by_id, record_student_attendance

# Add a global audio buffer that can be accessed by voice_listener.py
audio_buffer = queue.Queue(maxsize=100)  # Limit size to prevent memory issues

# Add a dictionary to track connected cameras
connected_cameras = {}

# Global frame buffers
current_frames = {}

# Configuration
HOST = '0.0.0.0'
PORT = 9090
MAX_FRAME_SIZE = 15 * 1024 * 1024  # 15MB max
TIMEOUT_SECONDS = 20
SHOW_FEED = True  # Enable window display for socket_server

# Add a flag to enable/disable verbose logging
VERBOSE_LOGGING = False  # Set to False to reduce log output and improve performance

# Frame buffer configuration
FRAME_BUFFER_SIZE = 2  # Number of frames to buffer for smooth playback

# Add a global variable for lecture_id
lecture_id = None

def log_if_verbose(message, level="INFO"):
    """Only log if verbose logging is enabled"""
    if VERBOSE_LOGGING:
        log_system_event(message, level)


FRAME_SIZE = {
    "RGB": (1080, 1920, 3),
    "IR": (424, 512),
    "DEPTH": (424, 512)
}


def normalize_ir(ir_raw):
    ir_normalized = ir_raw.astype(np.float32) / 65535.0
    amplified = np.clip(ir_normalized * 1.0, 0.0, 1.0)
    gamma_corrected = np.power(amplified, 0.32)
    return np.uint8(gamma_corrected * 255)


def normalize_depth(depth_raw):
    clipped = np.clip(depth_raw, 500, 4500)
    return cv2.convertScaleAbs(clipped, alpha=(255.0 / 4000.0))


# Socket receiver thread (similar to KinectReceiverThread in capture app)
class SocketReceiverThread(QThread):
    frame_received = pyqtSignal(dict)
    camera_connected = pyqtSignal(str)
    camera_disconnected = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = True
        self.server_socket = None

    def run(self):
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((HOST, PORT))
            self.server_socket.listen()
            log_system_event(f"Server ready at {HOST}:{PORT}")

            while self.running:
                try:
                    self.server_socket.settimeout(1.0)  # Allow checking self.running
                    conn, addr = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(conn, addr),
                        daemon=True
                    )
                    client_thread.start()
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        log_system_event(f"Error accepting connection: {e}", "ERROR")
                        self.error_occurred.emit(f"Error accepting connection: {e}")
        except Exception as e:
            log_system_event(f"Server error: {e}", "ERROR")
            self.error_occurred.emit(f"Server error: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()

    def handle_client(self, conn, addr):
        camera_id = None
        log_system_event(f"Connection from {addr}")

        try:
            conn.settimeout(TIMEOUT_SECONDS)

            while self.running:
                try:
                    # Receive header with more detailed logging
                    header_data = bytearray()
                    bytes_received = 0
                    expected_header_size = 20
                    
                    # Try to receive the full header in chunks
                    while bytes_received < expected_header_size:
                        chunk = conn.recv(expected_header_size - bytes_received)
                        if not chunk:
                            log_system_event(f"Connection closed during header reception from {addr}", "WARNING")
                            return
                        
                        header_data.extend(chunk)
                        bytes_received += len(chunk)
                        
                        # Log progress if partial header received
                        if bytes_received < expected_header_size:
                            log_system_event(f"Partial header received: {bytes_received}/{expected_header_size} bytes from {addr}", "DEBUG")
                    
                    # Check if we got the full header
                    if len(header_data) != expected_header_size:
                        log_system_event(f"Incomplete header from {addr}: received {len(header_data)}/{expected_header_size} bytes", "WARNING")
                        break
                    
                    # Log the raw header data for debugging
                    log_system_event(f"Header received from {addr}: {' '.join([f'{b:02x}' for b in header_data])}", "DEBUG")
                    
                    # Parse header
                    try:
                        seq = int.from_bytes(header_data[0:4], byteorder='little')
                        rgb_len = int.from_bytes(header_data[4:8], byteorder='little')
                        ir_len = int.from_bytes(header_data[8:12], byteorder='little')
                        depth_len = int.from_bytes(header_data[12:16], byteorder='little')
                        camera_id = int.from_bytes(header_data[16:20], byteorder='little')
                        
                        log_system_event(f"Parsed header: seq={seq}, rgb_len={rgb_len}, ir_len={ir_len}, depth_len={depth_len}, camera_id={camera_id}", "DEBUG")
                    except Exception as e:
                        log_system_event(f"Error parsing header from {addr}: {e}", "ERROR")
                        break

                    # Validate frame sizes
                    if any(l > MAX_FRAME_SIZE or l <= 0 for l in (rgb_len, ir_len, depth_len)):
                        log_system_event(f"Invalid frame sizes from {addr}", "WARNING")
                        conn.sendall(b"\x00")
                        break

                    # Register camera
                    if camera_id not in connected_cameras:
                        connected_cameras[camera_id] = {
                            'address': addr,
                            'last_frame_time': datetime.now(),
                            'frame_count': 0
                        }
                        self.camera_connected.emit(f"Camera {camera_id} connected from {addr}")
                    else:
                        connected_cameras[camera_id]['last_frame_time'] = datetime.now()
                        connected_cameras[camera_id]['frame_count'] += 1

                    # Receive frame data
                    def receive_data(size, name):
                        data = bytearray()
                        remaining = size

                        while remaining > 0:
                            chunk = conn.recv(min(65536, remaining))
                            if not chunk:
                                raise ConnectionError(f"Connection closed during {name} reception")
                            data.extend(chunk)
                            remaining -= len(chunk)

                        return data

                    try:
                        # Receive all frame data
                        rgb_data = receive_data(rgb_len, "RGB")
                        ir_data = receive_data(ir_len, "IR")
                        depth_data = receive_data(depth_len, "DEPTH")

                        # Decode frames
                        rgb_frame = cv2.imdecode(np.frombuffer(rgb_data, np.uint8), cv2.IMREAD_COLOR)
                        ir_frame = np.frombuffer(ir_data, dtype=np.uint16).reshape(FRAME_SIZE["IR"])
                        depth_frame = np.frombuffer(depth_data, dtype=np.uint16).reshape(FRAME_SIZE["DEPTH"])

                        # Send acknowledgment
                        conn.sendall(b"\x01")

                        # Emit signal with frames
                        self.frame_received.emit({
                            'camera_id': camera_id,
                            'rgb': rgb_frame,
                            'ir': ir_frame,
                            'depth': depth_frame
                        })

                    except Exception as e:
                        log_system_event(f"Error processing frames: {e}", "ERROR")
                        try:
                            conn.sendall(b"\x00")
                        except:
                            pass
                        break

                except socket.timeout:
                    log_system_event(f"Socket timeout with {addr}", "WARNING")
                    break
                except ConnectionError as e:
                    log_system_event(f"Connection error with {addr}: {e}", "WARNING")
                    break
                except Exception as e:
                    log_system_event(f"Error handling client {addr}: {e}", "ERROR")
                    break

        except Exception as e:
            log_system_event(f"Client handler error: {e}", "ERROR")
        finally:
            conn.close()
            if camera_id in connected_cameras:
                del connected_cameras[camera_id]
                self.camera_disconnected.emit(f"Camera {camera_id} disconnected")

    def stop(self):
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass


# Main window for displaying camera feeds
class AttendanceMonitorWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Set window properties
        self.setWindowTitle("Attendance Monitoring System")
        self.setMinimumSize(1200, 800)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create camera feeds container
        self.camera_feeds = {}
        self.camera_container = QWidget()
        self.camera_layout = QHBoxLayout(self.camera_container)
        main_layout.addWidget(self.camera_container)
        
        # Start socket receiver thread
        self.receiver_thread = SocketReceiverThread()
        self.receiver_thread.frame_received.connect(self.process_frames)
        self.receiver_thread.camera_connected.connect(self.on_camera_connected)
        self.receiver_thread.camera_disconnected.connect(self.on_camera_disconnected)
        self.receiver_thread.error_occurred.connect(self.on_error)
        self.receiver_thread.start()
        
        # Start frame update timer (60 FPS - same as capture app)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)
        self.update_timer.start(16)  # ~60 FPS
        
        # Start attendance processing timer (lower frequency)
        self.attendance_timer = QTimer()
        self.attendance_timer.timeout.connect(self.process_attendance)
        self.attendance_timer.start(200)  # 5 times per second
        
        # Start exit monitor thread
        threading.Thread(target=self.exit_monitor, daemon=True).start()
        
        # Start camera monitor thread
        threading.Thread(target=self.monitor_cameras, daemon=True).start()
        
        log_system_event("Attendance Monitor Window started")

    def process_frames(self, frames):
        """Process received frames from the socket thread"""
        camera_id = frames['camera_id']

        # Store frames in global buffer
        if camera_id not in current_frames:
            current_frames[camera_id] = {
                'buffer': [],  # Frame buffer for smooth playback
                'buffer_index': 0,
                'last_update': datetime.now(),
                'processing': False  # Lock to prevent concurrent processing
            }
        
        # Add frame to buffer
        frame_data = {
            'rgb': frames['rgb'],
            'ir': frames['ir'],
            'depth': frames['depth'],
            'timestamp': datetime.now(),
            'processed': False
        }
        
        # Manage buffer size
        if len(current_frames[camera_id]['buffer']) < FRAME_BUFFER_SIZE:
            current_frames[camera_id]['buffer'].append(frame_data)
        else:
            # Replace oldest frame
            oldest_idx = (current_frames[camera_id]['buffer_index'] + 1) % FRAME_BUFFER_SIZE
            current_frames[camera_id]['buffer'][oldest_idx] = frame_data
            current_frames[camera_id]['buffer_index'] = oldest_idx
        
        # Update timestamp
        current_frames[camera_id]['last_update'] = datetime.now()

    def update_displays(self):
        """Update the video displays with the latest frames - stable approach from capture app"""
        for camera_id, camera_data in current_frames.items():
            # Skip if no frames in buffer
            if not camera_data['buffer']:
                continue
            
            # Create camera feed widget if it doesn't exist
            if camera_id not in self.camera_feeds:
                self.add_camera_feed(camera_id)
            
            # Get the next frame from buffer (round-robin)
            buffer_idx = camera_data['buffer_index']
            frame_data = camera_data['buffer'][buffer_idx]
            
            # Update RGB display
            try:
                rgb_frame = frame_data['rgb']
                if rgb_frame is not None:
                    rgb_display = self.camera_feeds[camera_id]['rgb']
                    
                    # Get display dimensions
                    rgb_display_width = rgb_display.width()
                    rgb_display_height = rgb_display.height()
                    
                    # Calculate aspect ratio
                    h, w, ch = rgb_frame.shape
                    rgb_aspect = w / h
                    display_aspect = rgb_display_width / rgb_display_height
                    
                    # Calculate dimensions that preserve aspect ratio
                    if display_aspect > rgb_aspect:
                        # Display is wider than frame
                        target_height = rgb_display_height
                        target_width = int(target_height * rgb_aspect)
                    else:
                        # Display is taller than frame
                        target_width = rgb_display_width
                        target_height = int(target_width / rgb_aspect)
                    
                    # Resize RGB frame while preserving aspect ratio
                    rgb_resized = cv2.resize(rgb_frame, (target_width, target_height))
                    rgb_color = cv2.cvtColor(rgb_resized, cv2.COLOR_BGR2RGB)
                    
                    # Convert to QImage and then QPixmap
                    bytes_per_line = ch * target_width
                    rgb_qimg = QImage(rgb_color.data, target_width, target_height, bytes_per_line, QImage.Format_RGB888)
                    rgb_pixmap = QPixmap.fromImage(rgb_qimg)
                    
                    # Update the display
                    rgb_display.setPixmap(rgb_pixmap)
                    rgb_display.setAlignment(Qt.AlignCenter)
                
            except Exception as e:
                log_system_event(f"Error updating RGB display: {e}", "ERROR")
            
            # Update IR display
            try:
                ir_frame = frame_data['ir']
                if ir_frame is not None:
                    ir_display = self.camera_feeds[camera_id]['ir']
                    
                    # Enhance IR image
                    ir_enhanced = normalize_ir(ir_frame)
                    
                    # Get display dimensions
                    ir_display_width = ir_display.width()
                    ir_display_height = ir_display.height()
                    
                    # Calculate aspect ratio
                    h, w = ir_enhanced.shape
                    ir_aspect = w / h
                    display_aspect = ir_display_width / ir_display_height
                    
                    # Calculate dimensions that preserve aspect ratio
                    if display_aspect > ir_aspect:
                        # Display is wider than frame
                        target_height = ir_display_height
                        target_width = int(target_height * ir_aspect)
                    else:
                        # Display is taller than frame
                        target_width = ir_display_width
                        target_height = int(target_width / ir_aspect)
                    
                    # Resize IR frame while preserving aspect ratio
                    ir_resized = cv2.resize(ir_enhanced, (target_width, target_height))
                    
                    # Convert to QImage and then QPixmap
                    ir_qimg = QImage(ir_resized.data, target_width, target_height, target_width, QImage.Format_Grayscale8)
                    ir_pixmap = QPixmap.fromImage(ir_qimg)
                    
                    # Update the display
                    ir_display.setPixmap(ir_pixmap)
                    ir_display.setAlignment(Qt.AlignCenter)
                
            except Exception as e:
                log_system_event(f"Error updating IR display: {e}", "ERROR")
            
            # Update Depth display
            try:
                depth_frame = frame_data['depth']
                if depth_frame is not None:
                    depth_display = self.camera_feeds[camera_id]['depth']
                    
                    # Normalize depth for display
                    depth_normalized = normalize_depth(depth_frame)
                    
                    # Get display dimensions
                    depth_display_width = depth_display.width()
                    depth_display_height = depth_display.height()
                    
                    # Calculate aspect ratio
                    h, w = depth_normalized.shape
                    depth_aspect = w / h
                    display_aspect = depth_display_width / depth_display_height
                    
                    # Calculate dimensions that preserve aspect ratio
                    if display_aspect > depth_aspect:
                        # Display is wider than frame
                        target_height = depth_display_height
                        target_width = int(target_height * depth_aspect)
                    else:
                        # Display is taller than frame
                        target_width = depth_display_width
                        target_height = int(target_width / depth_aspect)
                    
                    # Resize depth frame while preserving aspect ratio
                    depth_resized = cv2.resize(depth_normalized, (target_width, target_height))
                    
                    # Convert to QImage and then QPixmap
                    depth_qimg = QImage(depth_resized.data, target_width, target_height, target_width, QImage.Format_Grayscale8)
                    depth_pixmap = QPixmap.fromImage(depth_qimg)
                    
                    # Update the display
                    depth_display.setPixmap(depth_pixmap)
                    depth_display.setAlignment(Qt.AlignCenter)
                
            except Exception as e:
                log_system_event(f"Error updating Depth display: {e}", "ERROR")

    def process_attendance(self):
        """Process frames for attendance in a separate step from display updates"""
        for camera_id, camera_data in current_frames.items():
            # Skip if no frames in buffer or already processing
            if not camera_data['buffer'] or camera_data.get('processing', False):
                continue
            
            # Mark as processing to prevent concurrent processing
            camera_data['processing'] = True
            
            try:
                # Get the oldest unprocessed frame
                unprocessed_frames = [
                    (i, frame) for i, frame in enumerate(camera_data['buffer']) 
                    if not frame.get('processed', False)
                ]
                
                if not unprocessed_frames:
                    camera_data['processing'] = False
                    continue
                    
                # Process the oldest unprocessed frame
                idx, frame_data = unprocessed_frames[0]
                
                # Get copies of the frames
                rgb_frame = frame_data['rgb'].copy() if frame_data['rgb'] is not None else None
                ir_frame = frame_data['ir'].copy() if frame_data['ir'] is not None else None
                depth_frame = frame_data['depth'].copy() if frame_data['depth'] is not None else None
                
                # Process the frame for attendance
                if rgb_frame is not None:
                    # Process in a separate thread to avoid blocking
                    threading.Thread(
                        target=self._process_attendance_frame,
                        args=(camera_id, idx, rgb_frame, ir_frame, depth_frame),
                        daemon=True
                    ).start()
                else:
                    camera_data['processing'] = False
                    
            except Exception as e:
                log_system_event(f"Error in attendance processing: {e}", "ERROR")
                camera_data['processing'] = False
    
    def _process_attendance_frame(self, camera_id, frame_idx, rgb_frame, ir_frame, depth_frame):
        """Process a single frame for attendance in a separate thread"""
        try:
            # Process the frame for attendance
            processed_frame = process_frame(rgb_frame, ir_frame, depth_frame, camera_id=camera_id)
            
            # Store the processed result
            if processed_frame is not None and camera_id in current_frames:
                # Make sure the buffer still exists and has the same frame
                if (len(current_frames[camera_id]['buffer']) > frame_idx and
                    current_frames[camera_id]['buffer'][frame_idx]['timestamp'] == 
                    current_frames[camera_id]['buffer'][frame_idx]['timestamp']):
                    
                    # Store processed frame
                    current_frames[camera_id]['buffer'][frame_idx]['processed_rgb'] = processed_frame
                    current_frames[camera_id]['buffer'][frame_idx]['processed'] = True
        except Exception as e:
            log_system_event(f"Error in attendance processing thread: {e}", "ERROR")
        finally:
            # Mark as no longer processing
            if camera_id in current_frames:
                current_frames[camera_id]['processing'] = False

    def add_camera_feed(self, camera_id):
        """Add a new camera feed to the UI"""
        # Create camera group box
        camera_group = QGroupBox(f"Camera {camera_id}")
        camera_layout = QVBoxLayout(camera_group)

        # Create RGB display
        rgb_group = QGroupBox("RGB")
        rgb_layout = QVBoxLayout(rgb_group)
        rgb_display = QLabel("Waiting for RGB feed...")
        rgb_display.setAlignment(Qt.AlignCenter)
        rgb_display.setMinimumSize(320, 180)
        rgb_display.setStyleSheet("background-color: black; color: white;")
        rgb_layout.addWidget(rgb_display)
        camera_layout.addWidget(rgb_group)

        # Create IR display
        ir_group = QGroupBox("IR")
        ir_layout = QVBoxLayout(ir_group)
        ir_display = QLabel("Waiting for IR feed...")
        ir_display.setAlignment(Qt.AlignCenter)
        ir_display.setMinimumSize(160, 120)
        ir_display.setStyleSheet("background-color: black; color: white;")
        ir_layout.addWidget(ir_display)
        camera_layout.addWidget(ir_group)

        # Create Depth display
        depth_group = QGroupBox("Depth")
        depth_layout = QVBoxLayout(depth_group)
        depth_display = QLabel("Waiting for Depth feed...")
        depth_display.setAlignment(Qt.AlignCenter)
        depth_display.setMinimumSize(160, 120)
        depth_display.setStyleSheet("background-color: black; color: white;")
        depth_layout.addWidget(depth_display)
        camera_layout.addWidget(depth_group)

        # Add to camera container
        self.camera_layout.addWidget(camera_group)

        # Store references to displays
        self.camera_feeds[camera_id] = {
            'rgb': rgb_display,
            'ir': ir_display,
            'depth': depth_display
        }

        log_system_event(f"Added display for Camera {camera_id}")

    def on_camera_connected(self, message):
        """Handle camera connection event"""
        log_system_event(message)

    def on_camera_disconnected(self, message):
        """Handle camera disconnection event"""
        log_system_event(message)

        # Extract camera ID from message
        try:
            camera_id = int(message.split()[1])

            # Remove camera feed from UI
            if camera_id in self.camera_feeds:
                # Find and remove the camera group box
                for i in range(self.camera_layout.count()):
                    widget = self.camera_layout.itemAt(i).widget()
                    if widget and widget.title() == f"Camera {camera_id}":
                        widget.setParent(None)
                        break

                # Remove from dictionary
                del self.camera_feeds[camera_id]

                # Remove from current_frames
                if camera_id in current_frames:
                    del current_frames[camera_id]
        except:
            pass

    def on_error(self, message):
        """Handle error event"""
        log_system_event(f"Error: {message}", "ERROR")

    def exit_monitor(self):
        """Monitor for exit conditions"""
        while True:
            handle_exit_check()
            reset_seen_now()

            if check_lecture_end():
                records, start, end = finalize_attendance()
                save_attendance(records, start, end, LECTURE_DURATION_MINUTES)

            time.sleep(5)

    def monitor_cameras(self):
        """Monitor connected cameras and log any issues"""
        while True:
            current_time = datetime.now()
            cameras_to_remove = []

            for camera_id, info in connected_cameras.items():
                # Check if camera has sent frames recently
                time_since_last_frame = (current_time - info['last_frame_time']).total_seconds()

                if time_since_last_frame > 30:  # No frames for 30 seconds
                    log_system_event(
                        f"Camera {camera_id} may be disconnected - no frames for {time_since_last_frame:.1f} seconds",
                        "WARNING")
                    cameras_to_remove.append(camera_id)

            # Remove disconnected cameras
            for camera_id in cameras_to_remove:
                if camera_id in connected_cameras:
                    del connected_cameras[camera_id]
                    log_system_event(f"Removing camera {camera_id} from active cameras")

            time.sleep(15)  # Check every 15 seconds

    def closeEvent(self, event):
        """Handle window close event"""
        # Stop the receiver thread
        self.receiver_thread.stop()
        self.receiver_thread.wait()
        
        # Stop timers
        self.update_timer.stop()
        self.attendance_timer.stop()
        
        # Finalize attendance
        records, start, end = finalize_attendance()
        save_attendance(records, start, end, LECTURE_DURATION_MINUTES)
        
        log_system_event("Attendance Monitor Window closed")
        event.accept()

# Add a function to get audio data from the buffer
def get_audio_chunk(timeout=0.1):
    """Get an audio chunk from the buffer, returns None if no data available"""
    try:
        return audio_buffer.get(timeout=timeout)
    except queue.Empty:
        return None

def start_server():
    """Start the socket server with PyQt5 UI"""
    global lecture_id
    
    # Check if lecture_id was provided as command-line argument
    if len(sys.argv) > 1:
        try:
            lecture_id = int(sys.argv[1])
            log_system_event(f"Starting camera server for lecture ID: {lecture_id}")
            
            # Initialize the attendance session with the lecture ID
            init_attendance_session(lecture_id)
        except ValueError:
            log_system_event(f"Invalid lecture ID: {sys.argv[1]}", "ERROR")
    
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle("Fusion")
    
    # Create and show the main window
    window = AttendanceMonitorWindow()
    window.show()
    
    # Start the application event loop
    sys.exit(app.exec_())

def init_attendance_session(lec_id):
    """Initialize the attendance session with the given lecture ID"""
    from attendance import init_attendance_session as init_session
    init_session(lec_id)

if __name__ == "__main__":
    start_server()
