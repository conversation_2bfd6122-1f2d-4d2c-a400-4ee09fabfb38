#!/usr/bin/env python3
"""
Production Control Center - Central GUI for managing all aspects of the Biometric Attendance System
Author: Dosumu Andrews
Version: 1.0.0
"""

import sys
import os
import subprocess
import threading
import time
import socket
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import sqlite3
from db import DB_PATH, get_system_settings, log_system_event
import psutil

class SystemMonitor(QThread):
    """Background thread for monitoring system components"""
    status_updated = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.running = True

    def run(self):
        while self.running:
            status = self.check_system_status()
            self.status_updated.emit(status)
            time.sleep(5)  # Check every 5 seconds

    def check_system_status(self):
        """Check status of all system components"""
        status = {
            'kinect_connected': self.check_kinect_connection(),
            'socket_server': self.check_socket_server(),
            'web_portal': self.check_web_portal(),
            'database': self.check_database(),
            'face_images': self.check_face_images(),
            'system_health': self.get_system_health()
        }
        return status

    def check_kinect_connection(self):
        """Check if Kinect is actually connected and streaming"""
        try:
            # Check if Kinect client process is running
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'KinectFaceSender' in proc.info['name'] or 'KinectFaceSender' in str(proc.info['cmdline']):
                    return {'status': 'connected', 'process_id': proc.info['pid']}
            return {'status': 'disconnected', 'process_id': None}
        except:
            return {'status': 'error', 'process_id': None}

    def check_socket_server(self):
        """Check if socket server is running and accepting connections"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 9090))
            sock.close()
            return {'status': 'running' if result == 0 else 'stopped', 'port': 9090}
        except:
            return {'status': 'error', 'port': 9090}

    def check_web_portal(self):
        """Check if web portal is accessible"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', 5000))
            sock.close()
            return {'status': 'running' if result == 0 else 'stopped', 'port': 5000}
        except:
            return {'status': 'error', 'port': 5000}

    def check_database(self):
        """Check database connectivity and integrity"""
        try:
            with sqlite3.connect(DB_PATH) as conn:
                c = conn.cursor()
                c.execute("SELECT COUNT(*) FROM students")
                student_count = c.fetchone()[0]
                c.execute("SELECT COUNT(*) FROM lectures")
                lecture_count = c.fetchone()[0]
                return {
                    'status': 'connected',
                    'students': student_count,
                    'lectures': lecture_count,
                    'path': DB_PATH
                }
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def check_face_images(self):
        """Check face images directory and count"""
        try:
            face_dir = "images/student_faces"
            if os.path.exists(face_dir):
                folders = [f for f in os.listdir(face_dir) if os.path.isdir(os.path.join(face_dir, f))]
                total_images = 0
                for folder in folders:
                    folder_path = os.path.join(face_dir, folder)
                    images = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    total_images += len(images)
                return {
                    'status': 'available',
                    'student_folders': len(folders),
                    'total_images': total_images,
                    'path': face_dir
                }
            else:
                return {'status': 'missing', 'path': face_dir}
        except Exception as e:
            return {'status': 'error', 'error': str(e)}

    def get_system_health(self):
        """Get overall system health metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'disk_usage': disk.percent,
                'memory_available': memory.available // (1024**3),  # GB
                'disk_free': disk.free // (1024**3)  # GB
            }
        except:
            return {'cpu_usage': 0, 'memory_usage': 0, 'disk_usage': 0}

    def stop(self):
        self.running = False

class LogViewer(QWidget):
    """Advanced log viewer with filtering and real-time updates"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_logs()

    def init_ui(self):
        layout = QVBoxLayout()

        # Filter controls
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("Filter by Level:"))

        self.level_combo = QComboBox()
        self.level_combo.addItems(["All", "INFO", "WARNING", "ERROR"])
        self.level_combo.currentTextChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.level_combo)

        filter_layout.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.textChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.search_input)

        self.auto_refresh = QCheckBox("Auto Refresh")
        self.auto_refresh.setChecked(True)
        filter_layout.addWidget(self.auto_refresh)

        layout.addLayout(filter_layout)

        # Log display
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(3)
        self.log_table.setHorizontalHeaderLabels(["Timestamp", "Level", "Message"])
        self.log_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.log_table)

        # Control buttons
        button_layout = QHBoxLayout()

        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.load_logs)
        button_layout.addWidget(refresh_btn)

        clear_btn = QPushButton("Clear Logs")
        clear_btn.clicked.connect(self.clear_logs)
        button_layout.addWidget(clear_btn)

        export_btn = QPushButton("Export Logs")
        export_btn.clicked.connect(self.export_logs)
        button_layout.addWidget(export_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # Auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_logs)
        self.refresh_timer.start(10000)  # Refresh every 10 seconds

    def load_logs(self):
        """Load logs from database"""
        if not self.auto_refresh.isChecked():
            return

        try:
            with sqlite3.connect(DB_PATH) as conn:
                conn.row_factory = sqlite3.Row
                c = conn.cursor()
                c.execute("""
                    SELECT timestamp, level, message
                    FROM system_logs
                    ORDER BY timestamp DESC
                    LIMIT 1000
                """)
                self.all_logs = [dict(row) for row in c.fetchall()]
                self.filter_logs()
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load logs: {e}")

    def filter_logs(self):
        """Filter logs based on level and search criteria"""
        if not hasattr(self, 'all_logs'):
            return

        level_filter = self.level_combo.currentText()
        search_text = self.search_input.text().lower()

        filtered_logs = []
        for log in self.all_logs:
            if level_filter != "All" and log['level'] != level_filter:
                continue
            if search_text and search_text not in log['message'].lower():
                continue
            filtered_logs.append(log)

        self.display_logs(filtered_logs)

    def display_logs(self, logs):
        """Display logs in the table"""
        self.log_table.setRowCount(len(logs))

        for i, log in enumerate(logs):
            self.log_table.setItem(i, 0, QTableWidgetItem(log['timestamp']))

            level_item = QTableWidgetItem(log['level'])
            if log['level'] == 'ERROR':
                level_item.setBackground(QColor(255, 200, 200))
            elif log['level'] == 'WARNING':
                level_item.setBackground(QColor(255, 255, 200))
            self.log_table.setItem(i, 1, level_item)

            self.log_table.setItem(i, 2, QTableWidgetItem(log['message']))

    def clear_logs(self):
        """Clear all logs from database"""
        reply = QMessageBox.question(self, "Clear Logs",
                                   "Are you sure you want to clear all logs?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                with sqlite3.connect(DB_PATH) as conn:
                    c = conn.cursor()
                    c.execute("DELETE FROM system_logs")
                    conn.commit()
                self.load_logs()
                log_system_event("System logs cleared via Control Center")
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Failed to clear logs: {e}")

    def export_logs(self):
        """Export logs to CSV file"""
        filename, _ = QFileDialog.getSaveFileName(self, "Export Logs",
                                                f"system_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                                                "CSV Files (*.csv)")
        if filename:
            try:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(['Timestamp', 'Level', 'Message'])
                    for log in self.all_logs:
                        writer.writerow([log['timestamp'], log['level'], log['message']])
                QMessageBox.information(self, "Success", f"Logs exported to {filename}")
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Failed to export logs: {e}")


class ProductionControlCenter(QMainWindow):
    """Main control center window"""

    def __init__(self):
        super().__init__()
        self.processes = {}  # Track running processes
        self.init_ui()
        self.start_monitoring()

    def init_ui(self):
        self.setWindowTitle("Biometric Attendance System - Production Control Center v1.0")
        self.setGeometry(100, 100, 1400, 900)
        self.setWindowIcon(QIcon("assets/icon.png") if os.path.exists("assets/icon.png") else self.style().standardIcon(QStyle.SP_ComputerIcon))

        # Central widget with tabs
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout()
        central_widget.setLayout(layout)

        # Create tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # System Overview Tab
        self.overview_tab = self.create_overview_tab()
        self.tab_widget.addTab(self.overview_tab, "System Overview")

        # Component Control Tab
        self.control_tab = self.create_control_tab()
        self.tab_widget.addTab(self.control_tab, "Component Control")

        # Log Viewer Tab
        self.log_viewer = LogViewer()
        self.tab_widget.addTab(self.log_viewer, "System Logs")

        # Settings Tab
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "Live Settings")

        # Face Management Tab
        self.face_tab = self.create_face_management_tab()
        self.tab_widget.addTab(self.face_tab, "Face Management")

        # Attendance Monitor Tab
        try:
            from attendance_monitor import AttendanceMonitorWidget
            self.attendance_monitor = AttendanceMonitorWidget()
            self.tab_widget.addTab(self.attendance_monitor, "Live Attendance")
        except ImportError:
            print("Attendance monitor not available")

        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Control Center Started")

        # Menu bar
        self.create_menu_bar()

    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('File')

        export_action = QAction('Export System Report', self)
        export_action.triggered.connect(self.export_system_report)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        exit_action = QAction('Exit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Tools menu
        tools_menu = menubar.addMenu('Tools')

        backup_action = QAction('Backup Database', self)
        backup_action.triggered.connect(self.backup_database)
        tools_menu.addAction(backup_action)

        import_faces_action = QAction('Import Face Images', self)
        import_faces_action.triggered.connect(self.import_face_images)
        tools_menu.addAction(import_faces_action)

        tools_menu.addSeparator()

        test_system_action = QAction('Test Attendance System', self)
        test_system_action.triggered.connect(self.test_attendance_system)
        tools_menu.addAction(test_system_action)

        # Help menu
        help_menu = menubar.addMenu('Help')

        about_action = QAction('About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_overview_tab(self):
        """Create system overview tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        # System status cards
        cards_layout = QGridLayout()

        # Kinect Status Card
        self.kinect_card = self.create_status_card("Kinect Sensor", "Checking...", QColor(200, 200, 200))
        cards_layout.addWidget(self.kinect_card, 0, 0)

        # Socket Server Card
        self.socket_card = self.create_status_card("Socket Server", "Checking...", QColor(200, 200, 200))
        cards_layout.addWidget(self.socket_card, 0, 1)

        # Web Portal Card
        self.web_card = self.create_status_card("Web Portal", "Checking...", QColor(200, 200, 200))
        cards_layout.addWidget(self.web_card, 1, 0)

        # Database Card
        self.db_card = self.create_status_card("Database", "Checking...", QColor(200, 200, 200))
        cards_layout.addWidget(self.db_card, 1, 1)

        layout.addLayout(cards_layout)

        # System Health Section
        health_group = QGroupBox("System Health")
        health_layout = QVBoxLayout()

        self.cpu_progress = QProgressBar()
        self.cpu_progress.setFormat("CPU: %p%")
        health_layout.addWidget(self.cpu_progress)

        self.memory_progress = QProgressBar()
        self.memory_progress.setFormat("Memory: %p%")
        health_layout.addWidget(self.memory_progress)

        self.disk_progress = QProgressBar()
        self.disk_progress.setFormat("Disk: %p%")
        health_layout.addWidget(self.disk_progress)

        health_group.setLayout(health_layout)
        layout.addWidget(health_group)

        # Recent Activity
        activity_group = QGroupBox("Recent System Activity")
        activity_layout = QVBoxLayout()

        self.activity_list = QListWidget()
        activity_layout.addWidget(self.activity_list)

        activity_group.setLayout(activity_layout)
        layout.addWidget(activity_group)

        widget.setLayout(layout)
        return widget

    def create_status_card(self, title, status, color):
        """Create a status card widget"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setLineWidth(2)
        card.setStyleSheet(f"QFrame {{ background-color: {color.name()}; border-radius: 10px; padding: 10px; }}")

        layout = QVBoxLayout()

        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        status_label = QLabel(status)
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)

        card.setLayout(layout)
        card.title_label = title_label
        card.status_label = status_label

        return card

    def create_control_tab(self):
        """Create component control tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Component Control Section
        control_group = QGroupBox("Component Control")
        control_layout = QGridLayout()

        # Kinect Client Control
        kinect_group = QGroupBox("Kinect Client (Visual Studio)")
        kinect_layout = QVBoxLayout()

        self.kinect_status_label = QLabel("Status: Not Running")
        kinect_layout.addWidget(self.kinect_status_label)

        kinect_btn_layout = QHBoxLayout()
        self.start_kinect_btn = QPushButton("Start Kinect Client")
        self.start_kinect_btn.clicked.connect(self.start_kinect_client)
        kinect_btn_layout.addWidget(self.start_kinect_btn)

        self.stop_kinect_btn = QPushButton("Stop Kinect Client")
        self.stop_kinect_btn.clicked.connect(self.stop_kinect_client)
        self.stop_kinect_btn.setEnabled(False)
        kinect_btn_layout.addWidget(self.stop_kinect_btn)

        kinect_layout.addLayout(kinect_btn_layout)
        kinect_group.setLayout(kinect_layout)
        control_layout.addWidget(kinect_group, 0, 0)

        # Socket Server Control
        socket_group = QGroupBox("Socket Server")
        socket_layout = QVBoxLayout()

        self.socket_status_label = QLabel("Status: Not Running")
        socket_layout.addWidget(self.socket_status_label)

        socket_btn_layout = QHBoxLayout()
        self.start_socket_btn = QPushButton("Start Socket Server")
        self.start_socket_btn.clicked.connect(self.start_socket_server)
        socket_btn_layout.addWidget(self.start_socket_btn)

        self.stop_socket_btn = QPushButton("Stop Socket Server")
        self.stop_socket_btn.clicked.connect(self.stop_socket_server)
        self.stop_socket_btn.setEnabled(False)
        socket_btn_layout.addWidget(self.stop_socket_btn)

        socket_layout.addLayout(socket_btn_layout)
        socket_group.setLayout(socket_layout)
        control_layout.addWidget(socket_group, 0, 1)

        # Web Portal Control
        web_group = QGroupBox("Web Portal")
        web_layout = QVBoxLayout()

        self.web_status_label = QLabel("Status: Not Running")
        web_layout.addWidget(self.web_status_label)

        web_btn_layout = QHBoxLayout()
        self.start_web_btn = QPushButton("Start Web Portal")
        self.start_web_btn.clicked.connect(self.start_web_portal)
        web_btn_layout.addWidget(self.start_web_btn)

        self.stop_web_btn = QPushButton("Stop Web Portal")
        self.stop_web_btn.clicked.connect(self.stop_web_portal)
        self.stop_web_btn.setEnabled(False)
        web_btn_layout.addWidget(self.stop_web_btn)

        self.open_web_btn = QPushButton("Open in Browser")
        self.open_web_btn.clicked.connect(self.open_web_portal)
        web_btn_layout.addWidget(self.open_web_btn)

        web_layout.addLayout(web_btn_layout)
        web_group.setLayout(web_layout)
        control_layout.addWidget(web_group, 1, 0)

        # Capture App Control
        capture_group = QGroupBox("Student Capture App")
        capture_layout = QVBoxLayout()

        self.capture_status_label = QLabel("Status: Not Running")
        capture_layout.addWidget(self.capture_status_label)

        capture_btn_layout = QHBoxLayout()
        self.start_capture_btn = QPushButton("Start Capture App")
        self.start_capture_btn.clicked.connect(self.start_capture_app)
        capture_btn_layout.addWidget(self.start_capture_btn)

        self.stop_capture_btn = QPushButton("Stop Capture App")
        self.stop_capture_btn.clicked.connect(self.stop_capture_app)
        self.stop_capture_btn.setEnabled(False)
        capture_btn_layout.addWidget(self.stop_capture_btn)

        capture_layout.addLayout(capture_btn_layout)
        capture_group.setLayout(capture_layout)
        control_layout.addWidget(capture_group, 1, 1)

        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # Quick Actions
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QHBoxLayout()

        start_all_btn = QPushButton("Start All Components")
        start_all_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        start_all_btn.clicked.connect(self.start_all_components)
        actions_layout.addWidget(start_all_btn)

        stop_all_btn = QPushButton("Stop All Components")
        stop_all_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        stop_all_btn.clicked.connect(self.stop_all_components)
        actions_layout.addWidget(stop_all_btn)

        restart_all_btn = QPushButton("Restart All Components")
        restart_all_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        restart_all_btn.clicked.connect(self.restart_all_components)
        actions_layout.addWidget(restart_all_btn)

        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)

        # Process Monitor
        process_group = QGroupBox("Running Processes")
        process_layout = QVBoxLayout()

        self.process_table = QTableWidget()
        self.process_table.setColumnCount(4)
        self.process_table.setHorizontalHeaderLabels(["Component", "PID", "Status", "Uptime"])
        self.process_table.horizontalHeader().setStretchLastSection(True)
        process_layout.addWidget(self.process_table)

        process_group.setLayout(process_layout)
        layout.addWidget(process_group)

        widget.setLayout(layout)
        return widget

    def create_settings_tab(self):
        """Create live settings management tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        settings_group = QGroupBox("Live System Settings")
        settings_layout = QFormLayout()

        # Load current settings
        try:
            current_settings = get_system_settings()
        except:
            current_settings = {}

        # Confidence Threshold
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(10, 90)
        self.confidence_slider.setValue(int(current_settings.get('confidence_threshold', 0.85) * 100))
        self.confidence_slider.valueChanged.connect(self.update_confidence_threshold)

        confidence_layout = QHBoxLayout()
        confidence_layout.addWidget(self.confidence_slider)
        self.confidence_label = QLabel(f"{self.confidence_slider.value()}%")
        confidence_layout.addWidget(self.confidence_label)

        settings_layout.addRow("Face Recognition Confidence:", confidence_layout)

        # Late Threshold
        self.late_threshold_spin = QSpinBox()
        self.late_threshold_spin.setRange(0, 60)
        self.late_threshold_spin.setValue(current_settings.get('late_threshold_minutes', 15))
        self.late_threshold_spin.valueChanged.connect(self.update_late_threshold)
        settings_layout.addRow("Late Threshold (minutes):", self.late_threshold_spin)

        # Depth Range
        depth_layout = QHBoxLayout()
        self.depth_min_spin = QSpinBox()
        self.depth_min_spin.setRange(100, 2000)
        self.depth_min_spin.setValue(current_settings.get('depth_range_mm', (500, 3000))[0])
        self.depth_min_spin.valueChanged.connect(self.update_depth_range)
        depth_layout.addWidget(self.depth_min_spin)

        depth_layout.addWidget(QLabel("to"))

        self.depth_max_spin = QSpinBox()
        self.depth_max_spin.setRange(1000, 5000)
        self.depth_max_spin.setValue(current_settings.get('depth_range_mm', (500, 3000))[1])
        self.depth_max_spin.valueChanged.connect(self.update_depth_range)
        depth_layout.addWidget(self.depth_max_spin)

        depth_layout.addWidget(QLabel("mm"))

        settings_layout.addRow("Depth Range:", depth_layout)

        # IR Threshold
        self.ir_threshold_spin = QSpinBox()
        self.ir_threshold_spin.setRange(100, 5000)
        self.ir_threshold_spin.setValue(current_settings.get('ir_reflectance_threshold', 1000))
        self.ir_threshold_spin.valueChanged.connect(self.update_ir_threshold)
        settings_layout.addRow("IR Reflectance Threshold:", self.ir_threshold_spin)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # Settings Status
        status_group = QGroupBox("Settings Status")
        status_layout = QVBoxLayout()

        self.settings_status_label = QLabel("Settings loaded successfully")
        self.settings_status_label.setStyleSheet("color: green;")
        status_layout.addWidget(self.settings_status_label)

        # Apply button
        apply_btn = QPushButton("Apply All Settings")
        apply_btn.clicked.connect(self.apply_all_settings)
        apply_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")
        status_layout.addWidget(apply_btn)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        widget.setLayout(layout)
        return widget

    def create_face_management_tab(self):
        """Create face management tab"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Face Import Section
        import_group = QGroupBox("Face Image Management")
        import_layout = QVBoxLayout()

        # Status display
        self.face_status_label = QLabel("Checking face images...")
        import_layout.addWidget(self.face_status_label)

        # Import button
        import_btn = QPushButton("Import Face Images from Folder")
        import_btn.clicked.connect(self.import_face_images)
        import_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        import_layout.addWidget(import_btn)

        # Scan button
        scan_btn = QPushButton("Scan for New Images")
        scan_btn.clicked.connect(self.scan_face_images)
        import_layout.addWidget(scan_btn)

        import_group.setLayout(import_layout)
        layout.addWidget(import_group)

        # Face Database Status
        db_group = QGroupBox("Face Database Status")
        db_layout = QFormLayout()

        self.students_with_faces_label = QLabel("Checking...")
        db_layout.addRow("Students with Face Encodings:", self.students_with_faces_label)

        self.total_face_images_label = QLabel("Checking...")
        db_layout.addRow("Total Face Images:", self.total_face_images_label)

        self.last_import_label = QLabel("Checking...")
        db_layout.addRow("Last Import:", self.last_import_label)

        db_group.setLayout(db_layout)
        layout.addWidget(db_group)

        widget.setLayout(layout)
        return widget

    def start_monitoring(self):
        """Start system monitoring"""
        self.monitor = SystemMonitor()
        self.monitor.status_updated.connect(self.update_system_status)
        self.monitor.start()

    def update_system_status(self, status):
        """Update UI with system status"""
        # Update Kinect status
        kinect_status = status['kinect_connected']
        if kinect_status['status'] == 'connected':
            self.update_status_card(self.kinect_card, "Connected", QColor(144, 238, 144))
            self.kinect_status_label.setText(f"Status: Connected (PID: {kinect_status['process_id']})")
            self.start_kinect_btn.setEnabled(False)
            self.stop_kinect_btn.setEnabled(True)
        else:
            self.update_status_card(self.kinect_card, "Disconnected", QColor(255, 182, 193))
            self.kinect_status_label.setText("Status: Not Running")
            self.start_kinect_btn.setEnabled(True)
            self.stop_kinect_btn.setEnabled(False)

        # Update Socket Server status
        socket_status = status['socket_server']
        if socket_status['status'] == 'running':
            self.update_status_card(self.socket_card, f"Running (Port {socket_status['port']})", QColor(144, 238, 144))
            self.socket_status_label.setText(f"Status: Running on port {socket_status['port']}")
            self.start_socket_btn.setEnabled(False)
            self.stop_socket_btn.setEnabled(True)
        else:
            self.update_status_card(self.socket_card, "Stopped", QColor(255, 182, 193))
            self.socket_status_label.setText("Status: Not Running")
            self.start_socket_btn.setEnabled(True)
            self.stop_socket_btn.setEnabled(False)

        # Update Web Portal status
        web_status = status['web_portal']
        if web_status['status'] == 'running':
            self.update_status_card(self.web_card, f"Running (Port {web_status['port']})", QColor(144, 238, 144))
            self.web_status_label.setText(f"Status: Running on port {web_status['port']}")
            self.start_web_btn.setEnabled(False)
            self.stop_web_btn.setEnabled(True)
        else:
            self.update_status_card(self.web_card, "Stopped", QColor(255, 182, 193))
            self.web_status_label.setText("Status: Not Running")
            self.start_web_btn.setEnabled(True)
            self.stop_web_btn.setEnabled(False)

        # Update Database status
        db_status = status['database']
        if db_status['status'] == 'connected':
            self.update_status_card(self.db_card, f"Connected ({db_status['students']} students)", QColor(144, 238, 144))
        else:
            self.update_status_card(self.db_card, "Error", QColor(255, 182, 193))

        # Update system health
        health = status['system_health']
        self.cpu_progress.setValue(int(health['cpu_usage']))
        self.memory_progress.setValue(int(health['memory_usage']))
        self.disk_progress.setValue(int(health['disk_usage']))

        # Update face images status
        face_status = status['face_images']
        if face_status['status'] == 'available':
            self.face_status_label.setText(f"Found {face_status['student_folders']} student folders with {face_status['total_images']} images")
            self.total_face_images_label.setText(str(face_status['total_images']))
        else:
            self.face_status_label.setText("Face images directory not found")

    def update_status_card(self, card, status, color):
        """Update a status card"""
        card.status_label.setText(status)
        card.setStyleSheet(f"QFrame {{ background-color: {color.name()}; border-radius: 10px; padding: 10px; }}")

    # Component Control Methods
    def start_kinect_client(self):
        """Start the Kinect client application"""
        try:
            kinect_path = "kinect_clients/KinectFaceSender/bin/Debug/KinectFaceSender.exe"
            if os.path.exists(kinect_path):
                process = subprocess.Popen([kinect_path], cwd="kinect_clients/KinectFaceSender/bin/Debug/")
                self.processes['kinect'] = process
                log_system_event("Kinect client started via Control Center")
                self.status_bar.showMessage("Kinect client started")
            else:
                QMessageBox.warning(self, "Error", f"Kinect client not found at {kinect_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start Kinect client: {e}")

    def stop_kinect_client(self):
        """Stop the Kinect client application"""
        try:
            if 'kinect' in self.processes:
                self.processes['kinect'].terminate()
                del self.processes['kinect']
            # Also try to kill any running KinectFaceSender processes
            for proc in psutil.process_iter(['pid', 'name']):
                if 'KinectFaceSender' in proc.info['name']:
                    proc.terminate()
            log_system_event("Kinect client stopped via Control Center")
            self.status_bar.showMessage("Kinect client stopped")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to stop Kinect client: {e}")

    def start_socket_server(self):
        """Start the socket server"""
        try:
            process = subprocess.Popen([sys.executable, "socket_server.py"], cwd=".")
            self.processes['socket'] = process
            log_system_event("Socket server started via Control Center")
            self.status_bar.showMessage("Socket server started")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start socket server: {e}")

    def stop_socket_server(self):
        """Stop the socket server"""
        try:
            if 'socket' in self.processes:
                self.processes['socket'].terminate()
                del self.processes['socket']
            log_system_event("Socket server stopped via Control Center")
            self.status_bar.showMessage("Socket server stopped")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to stop socket server: {e}")

    def start_web_portal(self):
        """Start the web portal"""
        try:
            process = subprocess.Popen([sys.executable, "main.py", "--mode", "web"], cwd=".")
            self.processes['web'] = process
            log_system_event("Web portal started via Control Center")
            self.status_bar.showMessage("Web portal started")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start web portal: {e}")

    def stop_web_portal(self):
        """Stop the web portal"""
        try:
            if 'web' in self.processes:
                self.processes['web'].terminate()
                del self.processes['web']
            log_system_event("Web portal stopped via Control Center")
            self.status_bar.showMessage("Web portal stopped")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to stop web portal: {e}")

    def start_capture_app(self):
        """Start the capture application"""
        try:
            process = subprocess.Popen([sys.executable, "kinect_capture_app.py"], cwd=".")
            self.processes['capture'] = process
            log_system_event("Capture app started via Control Center")
            self.status_bar.showMessage("Capture app started")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start capture app: {e}")

    def stop_capture_app(self):
        """Stop the capture application"""
        try:
            if 'capture' in self.processes:
                self.processes['capture'].terminate()
                del self.processes['capture']
            log_system_event("Capture app stopped via Control Center")
            self.status_bar.showMessage("Capture app stopped")
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to stop capture app: {e}")

    def open_web_portal(self):
        """Open web portal in browser"""
        import webbrowser
        webbrowser.open("http://localhost:5000")

    def start_all_components(self):
        """Start all system components"""
        self.start_socket_server()
        time.sleep(2)
        self.start_kinect_client()
        time.sleep(2)
        self.start_web_portal()
        log_system_event("All components started via Control Center")

    def stop_all_components(self):
        """Stop all system components"""
        self.stop_kinect_client()
        self.stop_socket_server()
        self.stop_web_portal()
        self.stop_capture_app()
        log_system_event("All components stopped via Control Center")

    def restart_all_components(self):
        """Restart all system components"""
        self.stop_all_components()
        time.sleep(3)
        self.start_all_components()
        log_system_event("All components restarted via Control Center")

    # Settings Methods
    def update_confidence_threshold(self, value):
        """Update confidence threshold setting"""
        self.confidence_label.setText(f"{value}%")

    def update_late_threshold(self, value):
        """Update late threshold setting"""
        pass

    def update_depth_range(self):
        """Update depth range setting"""
        pass

    def update_ir_threshold(self, value):
        """Update IR threshold setting"""
        pass

    def apply_all_settings(self):
        """Apply all settings to the system"""
        try:
            from db import update_system_setting

            # Update confidence threshold
            confidence = self.confidence_slider.value() / 100.0
            update_system_setting('confidence_threshold', confidence)

            # Update late threshold
            late_threshold = self.late_threshold_spin.value()
            update_system_setting('late_threshold_minutes', late_threshold)

            # Update depth range
            depth_min = self.depth_min_spin.value()
            depth_max = self.depth_max_spin.value()
            update_system_setting('depth_range_mm', (depth_min, depth_max))

            # Update IR threshold
            ir_threshold = self.ir_threshold_spin.value()
            update_system_setting('ir_reflectance_threshold', ir_threshold)

            # Update config file for immediate effect
            self.update_config_file()

            self.settings_status_label.setText("Settings applied successfully!")
            self.settings_status_label.setStyleSheet("color: green;")
            log_system_event("System settings updated via Control Center")

            QMessageBox.information(self, "Success", "Settings applied successfully!")

        except Exception as e:
            self.settings_status_label.setText(f"Error applying settings: {e}")
            self.settings_status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "Error", f"Failed to apply settings: {e}")

    def update_config_file(self):
        """Update config.py file with new settings"""
        try:
            config_content = f"""# Auto-generated configuration file
# Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# Depth sensing configuration (in millimeters)
depth_range_mm = ({self.depth_min_spin.value()}, {self.depth_max_spin.value()})

# IR reflectance threshold for liveness detection
ir_reflectance_threshold = {self.ir_threshold_spin.value()}

# Micro-movement detection parameters
micro_movement_jitter_px = 2.0
micro_movement_window_sec = 3.0

# Face recognition confidence threshold
confidence_threshold = {self.confidence_slider.value() / 100.0}

# Late arrival threshold in minutes
late_threshold_minutes = {self.late_threshold_spin.value()}
"""
            with open("config.py", "w") as f:
                f.write(config_content)

        except Exception as e:
            print(f"Error updating config file: {e}")

    # Face Management Methods
    def import_face_images(self):
        """Import face images to database"""
        try:
            from db import import_faces_to_db

            reply = QMessageBox.question(self, "Import Face Images",
                                       "This will scan the images/student_faces folder and import all face encodings to the database. Continue?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                # Show progress dialog
                progress = QProgressDialog("Importing face images...", "Cancel", 0, 0, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.show()

                # Run import in separate thread
                def import_worker():
                    try:
                        import_faces_to_db()
                        progress.close()
                        QMessageBox.information(self, "Success", "Face images imported successfully!")
                        log_system_event("Face images imported via Control Center")
                    except Exception as e:
                        progress.close()
                        QMessageBox.critical(self, "Error", f"Failed to import face images: {e}")

                thread = threading.Thread(target=import_worker)
                thread.start()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start import: {e}")

    def scan_face_images(self):
        """Scan for new face images"""
        try:
            face_dir = "images/student_faces"
            if os.path.exists(face_dir):
                folders = [f for f in os.listdir(face_dir) if os.path.isdir(os.path.join(face_dir, f))]
                total_images = 0
                for folder in folders:
                    folder_path = os.path.join(face_dir, folder)
                    images = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                    total_images += len(images)

                self.face_status_label.setText(f"Found {len(folders)} student folders with {total_images} images")
                self.total_face_images_label.setText(str(total_images))

                QMessageBox.information(self, "Scan Complete",
                                      f"Found {len(folders)} student folders with {total_images} total images")
            else:
                QMessageBox.warning(self, "Error", f"Face images directory not found: {face_dir}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to scan face images: {e}")

    # Utility Methods
    def backup_database(self):
        """Create a backup of the database"""
        try:
            import shutil
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"db/backup_attendance_{timestamp}.db"

            os.makedirs("db", exist_ok=True)
            shutil.copy2(DB_PATH, backup_path)

            QMessageBox.information(self, "Success", f"Database backed up to {backup_path}")
            log_system_event(f"Database backed up to {backup_path}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to backup database: {e}")

    def export_system_report(self):
        """Export a comprehensive system report"""
        try:
            filename, _ = QFileDialog.getSaveFileName(self, "Export System Report",
                                                    f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                                                    "Text Files (*.txt)")
            if filename:
                with open(filename, 'w') as f:
                    f.write("BIOMETRIC ATTENDANCE SYSTEM - SYSTEM REPORT\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    # Add system status information
                    f.write("SYSTEM STATUS:\n")
                    f.write("-" * 20 + "\n")
                    # Add more report details here

                QMessageBox.information(self, "Success", f"System report exported to {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to export system report: {e}")

    def test_attendance_system(self):
        """Run comprehensive system tests"""
        try:
            # Show progress dialog
            progress = QProgressDialog("Running system tests...", "Cancel", 0, 0, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            def run_tests():
                try:
                    import subprocess
                    result = subprocess.run([sys.executable, "test_attendance_system.py"],
                                          capture_output=True, text=True, cwd=".")

                    progress.close()

                    # Show results
                    if result.returncode == 0:
                        QMessageBox.information(self, "Test Results",
                                              "✅ All tests passed!\n\n" + result.stdout[-500:])
                    else:
                        QMessageBox.warning(self, "Test Results",
                                          "⚠️ Some tests failed:\n\n" + result.stdout[-500:])

                except Exception as e:
                    progress.close()
                    QMessageBox.critical(self, "Error", f"Failed to run tests: {e}")

            # Run in separate thread
            thread = threading.Thread(target=run_tests)
            thread.start()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to start tests: {e}")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "About",
                         "Biometric Attendance System\n"
                         "Production Control Center v1.0\n\n"
                         "Developed by Dosumu Andrews\n"
                         "Final Year Project\n\n"
                         "This control center provides centralized management\n"
                         "for all system components including Kinect client,\n"
                         "socket server, web portal, and face recognition system.")

    def closeEvent(self, event):
        """Handle application close event"""
        reply = QMessageBox.question(self, "Exit",
                                   "Are you sure you want to exit the Control Center?\n"
                                   "This will not stop running components.",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            if hasattr(self, 'monitor'):
                self.monitor.stop()
            log_system_event("Production Control Center closed")
            event.accept()
        else:
            event.ignore()


def main():
    """Main function to run the Production Control Center"""
    app = QApplication(sys.argv)
    app.setApplicationName("Biometric Attendance System - Control Center")
    app.setApplicationVersion("1.0.0")

    # Set application style
    app.setStyle("Fusion")

    # Apply dark theme
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    palette.setColor(QPalette.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
    palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
    palette.setColor(QPalette.Text, QColor(255, 255, 255))
    palette.setColor(QPalette.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
    palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
    palette.setColor(QPalette.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
    app.setPalette(palette)

    # Create and show main window
    window = ProductionControlCenter()
    window.show()

    # Log startup
    log_system_event("Production Control Center started")

    # Run application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()