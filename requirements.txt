face_recognition
opencv-python
numpy
pandas


above are the following modules you would need to downlaod to run this project
<----------------DOSUMU ANDREW-------------------->
you would need visual studio C++ desktop workload instaalled on your pc to be able to run this project
make sure you use python 3.10.xx as newer editions have conflicts with centrain modules and wheel builds
Multi-Modal Biometric System ✅ WORKING
RGB Images: ✅ Used for face recognition
IR Images: ✅ Used for liveness detection (reflectance threshold)
Depth Data: ✅ Used for 3D validation and anti-spoofing
Face Recognition: ✅ Uses face_recognition library with encoding caching
Advanced Anti-Spoofing ✅ WORKING
Depth Validation: ✅ Checks if face is within valid depth range (500-3000mm)
IR Reflectance: ✅ Validates infrared signature of real skin
Micro-Movement: ✅ Detects natural human micro-movements
Mask Detection: ✅ CNN-based mask detection with fallback methods